.\" Copyright (C) <PERSON>
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_agent_get_identity_path 3 "6 Mar 2019" "libssh2" "libssh2"
.SH NAME
libssh2_agent_get_identity_path - gets the custom ssh-agent socket path
.SH SYNOPSIS
.nf
#include <libssh2.h>

const char *
libssh2_agent_get_identity_path(LIBSSH2_AGENT *agent);
.fi
.SH DESCRIPTION
Returns the custom agent identity socket path if set using libssh2_agent_set_identity_path()

.SH RETURN VALUE
Returns the socket path on disk.
.SH AVAILABILITY
Added in libssh2 1.9
.SH SEE ALSO
.BR libssh2_agent_init(3)
.BR libssh2_agent_set_identity_path(3)
