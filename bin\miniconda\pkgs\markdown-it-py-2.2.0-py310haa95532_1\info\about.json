{"channels": ["https://repo.anaconda.com/pkgs/main"], "conda_build_version": "3.24.0", "conda_version": "23.3.1", "description": "Python port of markdown-it. Markdown parsing, done right!\n", "dev_url": "https://github.com/ExecutableBookProject/markdown-it-py", "doc_url": "https://github.com/ExecutableBookProject/markdown-it-py/blob/master/README.md", "env_vars": {"CIO_TEST": "<not set>"}, "extra": {"copy_test_source_files": true, "final": true, "flow_run_id": "a5518e75-6498-497e-8f7f-7ef7ddd2bb57", "recipe-maintainers": ["dopplershift", "ch<PERSON><PERSON><PERSON>"], "remote_url": "**************:AnacondaRecipes/markdown-it-py-feedstock.git", "sha": "72cbec664b2558efd026a48af6644e04bb5a5ab5"}, "home": "https://github.com/ExecutableBookProject/markdown-it-py", "identifiers": [], "keywords": [], "license": "MIT", "license_family": "MIT", "license_file": "LICENSE", "root_pkgs": ["abs-sdk 0.1.0+467.gb5e84daf py_0", "aiobotocore 2.4.2 py39haa95532_0", "aiohttp 3.8.3 py39h2bbff1b_0", "aioitertools 0.7.1 pyhd3eb1b0_0", "aiosignal 1.2.0 pyhd3eb1b0_0", "anaconda-client 1.11.2 py39haa95532_0", "appdirs 1.4.4 pyhd3eb1b0_0", "asn1crypto 1.5.1 py39haa95532_0", "async-timeout 4.0.2 py39haa95532_0", "attrs 22.1.0 py39haa95532_0", "beautifulsoup4 4.12.2 py39haa95532_0", "boltons 23.0.0 py39haa95532_0", "boto3 1.24.28 py39haa95532_0", "botocore 1.27.59 py39haa95532_0", "brotlipy 0.7.0 py39h2bbff1b_1003", "bzip2 1.0.8 he774522_0", "ca-certificates 2023.01.10 haa95532_0", "certifi 2023.5.7 py39haa95532_0", "cffi 1.15.1 py39h2bbff1b_3", "chardet 4.0.0 py39haa95532_1003", "charset-normalizer 2.0.4 pyhd3eb1b0_0", "click 8.0.4 py39haa95532_0", "cloudpickle 2.2.1 py39haa95532_0", "clyent 1.2.2 py39haa95532_1", "colorama 0.4.6 py39haa95532_0", "conda 23.3.1 py39haa95532_0", "conda-build 3.24.0 py39haa95532_0", "conda-package-handling 1.9.0 py39h8cc25b3_0", "conda-package-streaming 0.7.0 py39haa95532_0", "console_shortcut 0.1.1 4", "croniter 0.3.35 py_0", "cryptography 39.0.1 py39h21b164f_0", "dask-core 2023.4.1 py39haa95532_0", "datadog 0.42.0 pyhd3eb1b0_0", "defusedxml 0.7.1 pyhd3eb1b0_0", "deprecated 1.2.13 py39haa95532_0", "distributed 2023.4.1 py39haa95532_0", "docker-py 4.4.1 py39haa95532_5", "docker-pycreds 0.4.0 pyhd3eb1b0_0", "filelock 3.9.0 py39haa95532_0", "frozenlist 1.3.3 py39h2bbff1b_0", "fsspec 2023.4.0 py39haa95532_0", "gitdb 4.0.7 pyhd3eb1b0_0", "gitpython 3.1.30 py39haa95532_0", "glob2 0.7 pyhd3eb1b0_0", "heapdict 1.0.1 pyhd3eb1b0_0", "idna 3.4 py39haa95532_0", "importlib-metadata 6.0.0 py39haa95532_0", "importlib_resources 5.2.0 pyhd3eb1b0_1", "jinja2 3.1.2 py39haa95532_0", "jmespath 0.10.0 pyhd3eb1b0_0", "jsonpatch 1.32 pyhd3eb1b0_0", "jsonpointer 2.1 pyhd3eb1b0_0", "jsonschema 4.17.3 py39haa95532_0", "jupyter_core 5.3.0 py39haa95532_0", "libarchive 3.6.2 h2033e3e_1", "libiconv 1.16 h2bbff1b_2", "liblief 0.12.3 hd77b12b_0", "libxml2 2.10.3 h0ad7f3c_0", "locket 1.0.0 py39haa95532_0", "lz4-c 1.9.4 h2bbff1b_0", "m2-msys2-runtime 2.5.0.17080.65c939c 3", "m2-patch 2.7.5 2", "markupsafe 2.1.1 py39h2bbff1b_0", "marshmallow 3.19.0 py39haa95532_0", "marshmallow-oneofschema 3.0.1 py39haa95532_0", "menuinst 1.4.19 py39h59b6b97_0", "msgpack-python 1.0.3 py39h59b6b97_0", "msys2-conda-epoch 20160418 1", "multidict 6.0.2 py39h2bbff1b_0", "mypy_extensions 0.4.3 py39haa95532_1", "natsort 7.1.1 pyhd3eb1b0_0", "nbformat 5.7.0 py39haa95532_0", "openssl 1.1.1t h2bbff1b_0", "packaging 23.0 py39haa95532_0", "partd 1.2.0 pyhd3eb1b0_1", "pendulum 2.1.2 pyhd3eb1b0_1", "pip 23.0.1 py39haa95532_0", "pkginfo 1.9.6 py39haa95532_0", "platformdirs 2.5.2 py39haa95532_0", "pluggy 1.0.0 py39haa95532_1", "powershell_shortcut 0.0.1 3", "prefect 1.4.0 pyhd3eb1b0_0", "psutil 5.9.0 py39h2bbff1b_0", "py-lief 0.12.3 py39hd77b12b_0", "pycosat 0.6.4 py39h2bbff1b_0", "pycparser 2.21 pyhd3eb1b0_0", "pydantic 1.10.2 py39h2bbff1b_0", "pygithub 1.55 pyhd3eb1b0_1", "pyjwt 2.4.0 py39haa95532_0", "pynacl 1.5.0 py39h8cc25b3_0", "pyopenssl 23.0.0 py39haa95532_0", "pyparsing 3.0.9 py39haa95532_0", "pyrsistent 0.18.0 py39h196d8e1_0", "pysocks 1.7.1 py39haa95532_0", "python 3.9.16 h6244533_2", "python-box 5.4.1 pyhd3eb1b0_0", "python-dateutil 2.8.2 pyhd3eb1b0_0", "python-fastjsonschema 2.16.2 py39haa95532_0", "python-json-logger 2.0.7 py39haa95532_0", "python-libarchive-c 2.9 pyhd3eb1b0_1", "python-lmdb 1.4.1 py39hd77b12b_0", "python-slugify 5.0.2 pyhd3eb1b0_0", "pytz 2022.7 py39haa95532_0", "pytzdata 2020.1 pyhd3eb1b0_0", "pywin32 305 py39h2bbff1b_0", "pyyaml 6.0 py39h2bbff1b_1", "requests 2.29.0 py39haa95532_0", "requests-toolbelt 0.9.1 pyhd3eb1b0_0", "ruamel.yaml 0.17.21 py39h2bbff1b_0", "ruamel.yaml.clib 0.2.6 py39h2bbff1b_1", "ruamel_yaml 0.15.100 py39h2bbff1b_0", "s3transfer 0.6.0 py39haa95532_0", "setuptools 66.0.0 py39haa95532_0", "six 1.16.0 pyhd3eb1b0_1", "slack-sdk 3.19.5 pyhaa95532_0", "smmap 4.0.0 pyhd3eb1b0_0", "sortedcontainers 2.4.0 pyhd3eb1b0_0", "soupsieve 2.4 py39haa95532_0", "sqlite 3.41.2 h2bbff1b_0", "tabulate 0.8.10 py39haa95532_0", "tblib 1.7.0 pyhd3eb1b0_0", "text-unidecode 1.3 pyhd3eb1b0_0", "toml 0.10.2 pyhd3eb1b0_0", "tomli 2.0.1 py39haa95532_0", "toolz 0.12.0 py39haa95532_0", "tornado 6.2 py39h2bbff1b_0", "tqdm 4.65.0 py39hd4e2768_0", "traitlets 5.7.1 py39haa95532_0", "typing-extensions 4.5.0 py39haa95532_0", "typing_extensions 4.5.0 py39haa95532_0", "tzdata 2023c h04d1e81_0", "unidecode 1.2.0 pyhd3eb1b0_0", "urllib3 1.26.15 py39haa95532_0", "vc 14.2 h21ff451_1", "vs2015_runtime 14.27.29016 h5e58377_2", "websocket-client 0.58.0 py39haa95532_4", "wheel 0.38.4 py39haa95532_0", "win_inet_pton 1.1.0 py39haa95532_0", "wrapt 1.14.1 py39h2bbff1b_0", "xz 5.4.2 h8cc25b3_0", "yaml 0.2.5 he774522_0", "yarl 1.8.1 py39h2bbff1b_0", "zict 2.2.0 py39haa95532_0", "zipp 3.11.0 py39haa95532_0", "zlib 1.2.13 h8cc25b3_0", "zstandard 0.19.0 py39h2bbff1b_0", "zstd 1.5.5 hd43e919_0"], "summary": "Python port of markdown-it. Markdown parsing, done right!", "tags": []}