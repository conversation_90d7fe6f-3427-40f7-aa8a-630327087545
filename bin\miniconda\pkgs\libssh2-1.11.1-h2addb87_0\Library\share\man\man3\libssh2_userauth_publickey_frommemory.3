.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_userauth_publickey_frommemory 3 "1 Sep 2014" "libssh2" "libssh2"
.SH NAME
libssh2_userauth_publickey_frommemory - authenticate a session with a public key, read from memory
.SH SYNOPSIS
.nf
#include <libssh2.h>

int
libssh2_userauth_publickey_frommemory(LIBSSH2_SESSION *session,
                                      const char *username,
                                      size_t username_len,
                                      const char *publickeydata,
                                      size_t publickeydata_len,
                                      const char *privatekeydata,
                                      size_t privatekeydata_len,
                                      const char *passphrase);
.fi
.SH DESCRIPTION
\fIsession\fP - Session instance as returned by
.BR libssh2_session_init_ex(3)

\fIusername\fP - Remote user name to authenticate as.

\fIusername_len\fP - Length of username.

\fIpublickeydata\fP - Buffer containing the contents of a public key file.

\fIpublickeydata_len\fP - Length of public key data.

\fIprivatekeydata\fP - Buffer containing the contents of a private key file.

\fIprivatekeydata_len\fP - Length of private key data.

\fIpassphrase\fP - Passphrase to use when decoding private key file.

Attempt public key authentication using either a public key file or a PEM
encoded private key file stored in memory. When providing a private key, the
public key is automatically extracted from it. When providing both, the
passed public key takes precedence.
.SH RETURN VALUE
Return 0 on success or negative on failure. It returns
LIBSSH2_ERROR_EAGAIN when it would otherwise block. While
LIBSSH2_ERROR_EAGAIN is a negative number, it is not really a failure per se.
.SH ERRORS
\fILIBSSH2_ERROR_ALLOC\fP - An internal memory allocation call failed.

\fILIBSSH2_ERROR_SOCKET_SEND\fP - Unable to send data on socket.

\fILIBSSH2_ERROR_SOCKET_TIMEOUT\fP -

\fILIBSSH2_ERROR_PUBLICKEY_UNVERIFIED\fP - The username/public key
combination was invalid.

\fILIBSSH2_ERROR_AUTHENTICATION_FAILED\fP - Authentication using the supplied
public key was not accepted.
.SH AVAILABILITY
libssh2_userauth_publickey_frommemory was added in libssh2 1.6.0
Supported with OpenSSL, WinCNG, mbedTLS, OS/400 crypto backends.
.SH SEE ALSO
.BR libssh2_session_init_ex(3)
