{% set name = "mdurl" %}
{% set version = "0.1.0" %}

package:
  name: "{{ name|lower }}"
  version: "{{ version }}"

source:
  url: "https://pypi.io/packages/source/{{ name[0] }}/{{ name }}/{{ name }}-{{ version }}.tar.gz"
  sha256: 94873a969008ee48880fb21bad7de0349fef529f3be178969af5817239e9b990

build:
  number: 0
  script: "{{ PYTHON }} -m pip install . -vv"
  skip: True  # [py<36]

requirements:
  host:
    - flit-core >=3.2.0,<4
    - pip
    - setuptools
    - wheel
    - python
  run:
    - python

test:
  imports:
    - mdurl

about:
  home: "https://github.com/executablebooks/mdurl"
  license: MIT
  license_family: MIT
  license_file: LICENSE
  summary: "URL utilities for markdown-it-py parser."

extra:
  recipe-maintainers:
    - <PERSON><PERSON><PERSON><PERSON><PERSON>
