# This file created by conda-build 25.1.1
# meta.yaml template originally from:
# C:\b\abs_fblttj5gp1\clone\recipe, last modified Fri Feb  7 15:50:26 2025
# ------------------------------------------------

package:
  name: menuinst
  version: 2.2.0
source:
  sha256: c225bacefa9ee216e678d929d375e34f10856d87c1aba70a57c3d7974fda0cb7
  url: https://github.com/conda/menuinst/archive/2.2.0.tar.gz
build:
  missing_dso_whitelist: null
  number: '1'
  script:
    - del /q "C:\b\abs_fblttj5gp1\croot\menuinst_1738943438301\work\\menuinst\\data\\osx_launcher_*"
    - del /q "C:\b\abs_fblttj5gp1\croot\menuinst_1738943438301\work\\menuinst\\data\\appkit_launcher_*"
    - C:\\b\\abs_fblttj5gp1\\croot\\menuinst_1738943438301\\_h_env\\python.exe -m
      pip install . -vv --no-build-isolation --no-deps
    - copy "%SP_DIR%\\menuinst\\_legacy\cwp.py" "%PREFIX%\\"
  script_env:
    - SETUPTOOLS_SCM_PRETEND_VERSION=2.2.0
  skip_compile_pyc:
    - cwp.py
  string: py310h5da7b33_1
requirements:
  build:
    - bzip2 1.0.8 h2bbff1b_6
    - ca-certificates 2024.12.31 haa95532_0
    - libffi 3.4.4 hd77b12b_1
    - openssl 3.0.15 h827c3e9_0
    - python 3.10.16 h4607a30_1
    - sqlite 3.45.3 h2bbff1b_0
    - tk 8.6.14 h0416ee5_0
    - tzdata 2025a h04d1e81_0
    - vc 14.42 haa95532_3
    - vs2015_runtime 14.42.34433 he0abc0d_3
    - vs2019_win-64 19.29.30154 h96f319f_6
    - vswhere 2.8.4 haa95532_0
    - xz 5.4.6 h8cc25b3_1
    - zlib 1.2.13 h8cc25b3_1
  host:
    - bzip2 1.0.8 h2bbff1b_6
    - ca-certificates 2024.12.31 haa95532_0
    - libffi 3.4.4 hd77b12b_1
    - openssl 3.0.15 h827c3e9_0
    - packaging 24.2 py310haa95532_0
    - pip 25.0 py310haa95532_0
    - python 3.10.16 h4607a30_1
    - setuptools 75.8.0 py310haa95532_0
    - setuptools-scm 8.1.0 py310haa95532_0
    - setuptools_scm 8.1.0 hd3eb1b0_0
    - sqlite 3.45.3 h2bbff1b_0
    - tk 8.6.14 h0416ee5_0
    - tomli 2.0.1 py310haa95532_0
    - tzdata 2025a h04d1e81_0
    - vc 14.42 haa95532_3
    - vs2015_runtime 14.42.34433 he0abc0d_3
    - wheel 0.45.1 py310haa95532_0
    - xz 5.4.6 h8cc25b3_1
    - zlib 1.2.13 h8cc25b3_1
  run:
    - python >=3.10,<3.11.0a0
    - vc >=14.2,<15.0a0
    - vs2015_runtime >=14.29.30133,<15.0a0
test:
  commands:
    - pip check
    - pytest tests/ -vvv --ignore=tests/test_schema.py --ignore=tests/test_elevation.py
  imports:
    - menuinst
    - menuinst._legacy
    - menuinst.api
    - menuinst.platforms.win_utils.winshortcut
  requires:
    - conda
    - pip
    - pydantic
    - pytest
    - pytest-mock
  source_files:
    - tests
about:
  description: 'This package provides cross platform menu item installation for conda
    packages.


    If a conda package ships a menuinst JSON document under $PREFIX/Menu, conda will
    invoke

    menuinst to process the JSON file and install the menu items in your operating
    system.

    The menu items are removed when the package is uninstalled.

    '
  dev_url: https://github.com/conda/menuinst/
  doc_url: https://conda.github.io/menuinst/
  home: https://github.com/conda/menuinst/
  license: BSD-3-Clause AND MIT
  license_family: BSD
  license_file:
    - LICENSE.txt
    - menuinst/_vendor/apipkg/LICENSE
  summary: cross platform install of menu items
extra:
  copy_test_source_files: true
  final: true
  flow_run_id: fbd3cbc4-d810-4c25-b1d0-eb0bbc6c6ace
  recipe-maintainers:
    - carlodri
    - goanpeca
    - isuruf
    - jaimergp
    - jakirkham
  remote_url: **************:AnacondaRecipes/menuinst-feedstock.git
  sha: 373dbc66843207dab781c10edc7216e3f81cd5c2
