.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_sftp_rmdir 3 "20 Feb 2010" "libssh2 1.2.4" "libssh2"
.SH NAME
libssh2_sftp_rmdir - convenience macro for \fIlibssh2_sftp_rmdir_ex(3)\fP
.SH SYNOPSIS
.nf
#include <libssh2.h>
#include <libssh2_sftp.h>

#define libssh2_sftp_rmdir(sftp, path) \\
    libssh2_sftp_rmdir_ex((sftp), (path), strlen(path))
.fi
.SH DESCRIPTION
This is a macro defined in a public libssh2 header file that is using the
underlying function \fIlibssh2_sftp_rmdir_ex(3)\fP.
.SH RETURN VALUE
See \fIlibssh2_sftp_rmdir_ex(3)\fP
.SH ERRORS
See \fIlibssh2_sftp_rmdir_ex(3)\fP
.SH SEE ALSO
.BR libssh2_sftp_rmdir_ex(3)
