# This file created by conda-build 24.11.1
# meta.yaml template originally from:
# C:\b\abs_3f93ve2oe0\clone\recipe, last modified Fri Nov 29 14:38:34 2024
# ------------------------------------------------

package:
  name: libssh2
  version: 1.11.1
source:
  sha256: d9ec76cbe34db98eec3539fe2c899d26b0c837cb3eb466a56b0f109cabf658f7
  url: https://www.libssh2.org/download/libssh2-1.11.1.tar.gz
build:
  number: '0'
  run_exports:
    - libssh2 >=1.11.1,<2.0a0
  string: h2addb87_0
requirements:
  build:
    - cmake-no-system 3.25.3 h6c2663c_0
    - ninja-base 1.12.1 h4157e71_0
    - vs2015_runtime 14.40.33807 h98bb1dd_1
    - vs2019_win-64 19.29.30154 h96f319f_5
    - vswhere 2.8.4 haa95532_0
  host:
    - ca-certificates 2024.11.26 haa95532_0
    - openssl 3.0.15 h827c3e9_0
    - vc 14.40 h2eaa2aa_1
    - vs2015_runtime 14.40.33807 h98bb1dd_1
    - zlib 1.2.13 h8cc25b3_1
  run:
    - openssl >=3.0.15,<4.0a0
    - vc >=14.2,<15.0a0
    - vs2015_runtime >=14.29.30133,<15.0a0
    - zlib >=1.2.13,<1.3.0a0
test:
  commands:
    - if not exist %LIBRARY_INC%\\libssh2.h exit 1
    - if not exist %LIBRARY_INC%\\libssh2_publickey.h exit 1
    - if not exist %LIBRARY_INC%\\libssh2_sftp.h exit 1
    - if not exist %LIBRARY_LIB%\\libssh2.lib exit 1
    - if not exist %LIBRARY_LIB%\\pkgconfig\\libssh2.pc exit 1
    - if not exist %LIBRARY_BIN%\\libssh2.dll exit 1
about:
  description: 'libssh2 is a library implementing the SSH2 protocol, available under
    the revised BSD license.

    '
  dev_url: https://github.com/libssh2/libssh2
  doc_url: https://www.libssh2.org/docs.html
  home: https://www.libssh2.org/
  license: BSD-3-Clause
  license_family: BSD
  license_file: COPYING
  summary: the SSH library
extra:
  copy_test_source_files: true
  final: true
  flow_run_id: 3fbb4cd9-cc43-41a6-9906-70b29538c044
  recipe-maintainers:
    - shadowwalkersb
  remote_url: **************:AnacondaRecipes/libssh2-feedstock.git
  sha: ccf17c18a57eafac668302e8b68bd89ce4526611
