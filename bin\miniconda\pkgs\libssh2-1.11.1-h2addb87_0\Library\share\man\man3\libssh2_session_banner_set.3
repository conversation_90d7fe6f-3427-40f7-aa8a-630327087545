.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_session_banner_set 3 "9 Sep 2011" "libssh2" "libssh2"
.SH NAME
libssh2_session_banner_set - set the SSH protocol banner for the local client
.SH SYNOPSIS
.nf
#include <libssh2.h>

int
libssh2_session_banner_set(LIBSSH2_SESSION *session, const char *banner);
.fi
.SH DESCRIPTION
\fIsession\fP - Session instance as returned by
.BR libssh2_session_init_ex(3)

\fIbanner\fP - A pointer to a zero-terminated string holding the user defined
banner

Set the banner that will be sent to the remote host when the SSH session is
started with \fIlibssh2_session_handshake(3)\fP This is optional; a banner
corresponding to the protocol and libssh2 version will be sent by default.
.SH RETURN VALUE
Returns 0 on success or negative on failure. It returns LIBSSH2_ERROR_EAGAIN
when it would otherwise block. While LIBSSH2_ERROR_EAGAIN is a negative
number, it is not really a failure per se.
.SH ERRORS
\fILIBSSH2_ERROR_ALLOC\fP - An internal memory allocation call failed.
.SH AVAILABILITY
Added in 1.4.0.

Before 1.4.0 this function was known as libssh2_banner_set(3)
.SH SEE ALSO
.BR libssh2_session_handshake(3),
.BR libssh2_session_banner_get(3)
