{"channels": ["https://repo.anaconda.com/pkgs/main"], "conda_build_version": "24.3.0", "conda_version": "24.3.0", "description": "LZ4 is lossless compression algorithm, providing compression speed at 400\nMB/s per core (0.16 Bytes/cycle). It features an extremely fast decoder,\nwith speed in multiple GB/s per core (0.71 Bytes/cycle). A high compression\nderivative, called LZ4_HC, is available, trading customizable CPU time for\ncompression ratio. LZ4 library is provided as open source software using a\nBSD license.\n", "dev_url": "https://github.com/lz4/lz4", "doc_url": "https://github.com/lz4/lz4/blob/dev/README.md", "env_vars": {"CIO_TEST": "<not set>"}, "extra": {"copy_test_source_files": true, "final": true, "flow_run_id": "fd236b1e-d58b-461e-afcc-d51a3b0a0080", "recipe-maintainers": ["mingwandroid", "rmax", "wesm", "xhochy"], "remote_url": "**************:AnacondaRecipes/lz4-c-feedstock.git", "sha": "45537caf4f5443bf05cd61697e4cc857567e2aee"}, "home": "https://lz4.github.io/lz4/", "identifiers": [], "keywords": [], "license": "BSD-2-<PERSON><PERSON>", "license_family": "BSD", "license_file": "lib/LICENSE", "root_pkgs": ["abs-sdk 0.1.0+1307.gd190520f py_0", "aiobotocore 2.7.0 py39haa95532_0", "aiohttp 3.9.3 py39h2bbff1b_0", "aioitertools 0.7.1 pyhd3eb1b0_0", "aiosignal 1.2.0 pyhd3eb1b0_0", "anaconda-anon-usage 0.4.4 py39hfc23b7f_100", "anaconda-channel-scanner 1.3.0 py_1", "anaconda-client 1.12.3 py39haa95532_0", "appdirs 1.4.4 pyhd3eb1b0_0", "archspec 0.2.3 pyhd3eb1b0_0", "asn1crypto 1.5.1 py39haa95532_0", "async-timeout 4.0.3 py39haa95532_0", "attrs 23.1.0 py39haa95532_0", "beautifulsoup4 4.12.2 py39haa95532_0", "boltons 23.0.0 py39haa95532_0", "boto3 1.28.64 py39haa95532_0", "botocore 1.31.64 py39haa95532_0", "brotli-python 1.0.9 py39hd77b12b_7", "bzip2 1.0.8 h2bbff1b_5", "ca-certificates 2024.3.11 haa95532_0", "certifi 2024.2.2 py39haa95532_0", "cffi 1.16.0 py39h2bbff1b_0", "chardet 4.0.0 py39haa95532_1003", "charset-normalizer 2.0.4 pyhd3eb1b0_0", "click 8.1.7 py39haa95532_0", "cloudpickle 2.2.1 py39haa95532_0", "colorama 0.4.6 py39haa95532_0", "conda 24.3.0 py39haa95532_0", "conda-build 24.3.0 py39haa95532_0", "conda-content-trust 0.2.0 py39haa95532_0", "conda-index 0.4.0 pyhd3eb1b0_0", "conda-libmamba-solver 24.1.0 pyhd3eb1b0_0", "conda-package-handling 2.2.0 py39haa95532_0", "conda-package-streaming 0.9.0 py39haa95532_0", "console_shortcut_miniconda 0.1.1 haa95532_1", "croniter 0.3.35 py_0", "cryptography 42.0.5 py39h89fc84f_0", "dask-core 2023.11.0 py39haa95532_0", "datadog 0.42.0 pyhd3eb1b0_0", "defusedxml 0.7.1 pyhd3eb1b0_0", "deprecated 1.2.13 py39haa95532_0", "distributed 2023.11.0 py39haa95532_0", "distro 1.8.0 py39haa95532_0", "docker-py 4.4.1 py39haa95532_5", "docker-pycreds 0.4.0 pyhd3eb1b0_0", "filelock 3.13.1 py39haa95532_0", "fmt 9.1.0 h6d14046_0", "frozenlist 1.4.0 py39h2bbff1b_0", "fsspec 2023.10.0 py39haa95532_0", "gitdb 4.0.7 pyhd3eb1b0_0", "gitpython 3.1.37 py39haa95532_0", "heapdict 1.0.1 pyhd3eb1b0_0", "idna 3.4 py39haa95532_0", "importlib-metadata 7.0.1 py39haa95532_0", "importlib_resources 6.1.1 py39haa95532_1", "jinja2 3.1.3 py39haa95532_0", "jmespath 1.0.1 py39haa95532_0", "jsonpatch 1.33 py39haa95532_0", "jsonpointer 2.1 pyhd3eb1b0_0", "jsonschema 4.19.2 py39haa95532_0", "jsonschema-specifications 2023.7.1 py39haa95532_0", "jupyter_core 5.5.0 py39haa95532_0", "libarchive 3.6.2 hb62f4d4_2", "libcurl 8.5.0 h86230a5_0", "libiconv 1.16 h2bbff1b_2", "liblief 0.12.3 hd77b12b_0", "libmamba 1.5.8 h99b1521_1", "libmambapy 1.5.8 py39h77c03ed_1", "libsolv 0.7.24 h23ce68f_0", "libssh2 1.10.0 he2ea4bf_2", "libxml2 2.10.4 h0ad7f3c_2", "locket 1.0.0 py39haa95532_0", "lz4-c 1.9.4 h2bbff1b_0", "m2-msys2-runtime 2.5.0.17080.65c939c 3", "m2-patch 2.7.5 2", "markupsafe 2.1.3 py39h2bbff1b_0", "marshmallow 3.19.0 py39haa95532_0", "marshmallow-oneofschema 3.0.1 py39haa95532_0", "menuinst 2.0.2 py39hd77b12b_0", "more-itertools 10.1.0 py39haa95532_0", "msgpack-python 1.0.3 py39h59b6b97_0", "msys2-conda-epoch 20160418 1", "multidict 6.0.4 py39h2bbff1b_0", "mypy_extensions 1.0.0 py39haa95532_0", "natsort 7.1.1 pyhd3eb1b0_0", "nbformat 5.9.2 py39haa95532_0", "openssl 3.0.13 h2bbff1b_0", "packaging 23.2 py39haa95532_0", "partd 1.4.1 py39haa95532_0", "pcre2 10.42 h0ff8eda_0", "pendulum 2.1.2 pyhd3eb1b0_1", "percy 0.1.0 pyhd3eb1b0_0", "pip 23.3.1 py39haa95532_0", "pkginfo 1.9.6 py39haa95532_0", "platformdirs 3.10.0 py39haa95532_0", "pluggy 1.0.0 py39haa95532_1", "powershell_shortcut_miniconda 0.0.1 haa95532_1", "prefect 1.4.0 pyhd3eb1b0_0", "psutil 5.9.0 py39h2bbff1b_0", "py-lief 0.12.3 py39hd77b12b_0", "pybind11-abi 4 hd3eb1b0_1", "pycosat 0.6.6 py39h2bbff1b_0", "pycparser 2.21 pyhd3eb1b0_0", "pydantic 1.10.12 py39h2bbff1b_1", "pygithub 1.55 pyhd3eb1b0_1", "pyjwt 2.4.0 py39haa95532_0", "pynacl 1.5.0 py39h8cc25b3_0", "pyopenssl 24.0.0 py39haa95532_0", "pyparsing 3.0.9 py39haa95532_0", "pysocks 1.7.1 py39haa95532_0", "python 3.9.18 h1aa4202_0", "python-box 5.4.1 pyhd3eb1b0_0", "python-dateutil 2.8.2 pyhd3eb1b0_0", "python-fastjsonschema 2.16.2 py39haa95532_0", "python-json-logger 2.0.7 py39haa95532_0", "python-libarchive-c 2.9 pyhd3eb1b0_1", "python-lmdb 1.4.1 py39hd77b12b_0", "python-slugify 5.0.2 pyhd3eb1b0_0", "pytz 2024.1 py39haa95532_0", "pytzdata 2020.1 pyhd3eb1b0_0", "pywin32 305 py39h2bbff1b_0", "pyyaml 6.0.1 py39h2bbff1b_0", "referencing 0.30.2 py39haa95532_0", "reproc 14.2.4 hd77b12b_1", "reproc-cpp 14.2.4 hd77b12b_1", "requests 2.31.0 py39haa95532_1", "requests-toolbelt 1.0.0 py39haa95532_0", "rpds-py 0.10.6 py39h062c2fa_0", "ruamel.yaml 0.17.21 py39h2bbff1b_0", "ruamel.yaml.clib 0.2.6 py39h2bbff1b_1", "s3transfer 0.7.0 py39haa95532_0", "setuptools 68.2.2 py39haa95532_0", "six 1.16.0 pyhd3eb1b0_1", "slack-sdk 3.19.5 pyhaa95532_0", "smmap 4.0.0 pyhd3eb1b0_0", "sortedcontainers 2.4.0 pyhd3eb1b0_0", "soupsieve 2.5 py39haa95532_0", "sqlite 3.41.2 h2bbff1b_0", "tabulate 0.9.0 py39haa95532_0", "tblib 1.7.0 pyhd3eb1b0_0", "termcolor 2.1.0 py39haa95532_0", "text-unidecode 1.3 pyhd3eb1b0_0", "toml 0.10.2 pyhd3eb1b0_0", "tomli 2.0.1 py39haa95532_0", "toolz 0.12.0 py39haa95532_0", "tornado 6.3.3 py39h2bbff1b_0", "tqdm 4.65.0 py39hd4e2768_0", "traitlets 5.7.1 py39haa95532_0", "typing-extensions 4.9.0 py39haa95532_1", "typing_extensions 4.9.0 py39haa95532_1", "tzdata 2024a h04d1e81_0", "unidecode 1.2.0 pyhd3eb1b0_0", "urllib3 1.26.18 py39haa95532_0", "vc 14.2 h21ff451_1", "vs2015_runtime 14.27.29016 h5e58377_2", "websocket-client 0.58.0 py39haa95532_4", "wheel 0.41.2 py39haa95532_0", "win_inet_pton 1.1.0 py39haa95532_0", "wrapt 1.14.1 py39h2bbff1b_0", "xz 5.4.6 h8cc25b3_0", "yaml 0.2.5 he774522_0", "yaml-cpp 0.8.0 hd77b12b_0", "yarl 1.9.3 py39h2bbff1b_0", "zict 3.0.0 py39haa95532_0", "zipp 3.17.0 py39haa95532_0", "zlib 1.2.13 h8cc25b3_0", "zstandard 0.19.0 py39h2bbff1b_0", "zstd 1.5.5 hd43e919_0"], "summary": "Extremely Fast Compression algorithm", "tags": []}