<?xml version="1.0"?>
<HTML xmlns:pp="http://www.isogen.com/paul/post-processor">
<TITLE>Introduction to XSL</TITLE>
<H1>Introduction to XSL</H1>



                <HR/>
                <H2>Overview
</H2>
                <UL>

        <LI>1.Intro</LI>

        <LI>2.History</LI>

        <LI>3.XSL Basics</LI>

        <LI>Lunch</LI>

        <LI>4.An XML Data Model</LI>

        <LI>5.XSL Patterns</LI>

        <LI>6.XSL Templates</LI>

        <LI>7.XSL Formatting Model
</LI>

                </UL>






                <HR/>
                <H2>Intro</H2>
                <UL>

        <LI>Who am I?</LI>

        <LI>Who are you?</LI>

        <LI>Why are we here?
</LI>

                </UL>






                <HR/>
                <H2>History: XML and SGML</H2>
                <UL>

        <LI>XML is a subset of SGML.</LI>

        <LI>SGML allows the separation of abstract content from formatting.</LI>

        <LI>Also one of XML's primary virtues (in the doc publishing domain).
</LI>

                </UL>






                <HR/>
                <H2>History: What are stylesheets?</H2>
                <UL>

        <LI>Stylesheets specify the formatting of SGML/XML documents.</LI>

        <LI>Stylesheets put the &quot;style&quot; back into documents.</LI>

        <LI>New York Times content+NYT Stylesheet = NYT paper
</LI>

                </UL>






                <HR/>
                <H2>History: FOSI</H2>
                <UL>

        <LI>FOSI: &quot;Formatted Output Specification Instance&quot;
<UL>
        <LI>MIL-STD-28001
        </LI>

        <LI>FOSI's are SGML documents
        </LI>

        <LI>A stylesheet for another document
        </LI>
</UL></LI>

        <LI>Obsolete but implemented...
</LI>

                </UL>




</HTML>
