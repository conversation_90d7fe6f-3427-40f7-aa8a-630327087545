###########################################################################
# libssh2 installation details
#
# Copyright (C) The libssh2 project and its contributors.
# SPDX-License-Identifier: BSD-3-Clause
###########################################################################

prefix=C:/b/abs_3f93ve2oe0/croot/libssh2_1732891126529/_h_env/Library
exec_prefix=${prefix}
libdir=${exec_prefix}/lib
includedir=${prefix}/include

Name: libssh2
URL: https://libssh2.org/
Description: Library for SSH-based communication
Version: 1.11.1
Requires: 
Requires.private: libcrypto,zlib
Libs: -L${libdir} -lssh2 
Libs.private: -lws2_32 -lcrypto -lcrypt32 -lbcrypt C:/b/abs_3f93ve2oe0/croot/libssh2_1732891126529/_h_env/Library/lib/z.lib
Cflags: -I${includedir}
