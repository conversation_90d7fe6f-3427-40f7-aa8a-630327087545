# This file created by conda-build 24.3.0
# meta.yaml template originally from:
# C:\b\abs_fdogc_e7a1\clone\recipe, last modified Tue Apr 30 22:19:38 2024
# ------------------------------------------------

package:
  name: lz4-c
  version: 1.9.4
source:
  fn: lz4-1.9.4.tar.gz
  sha256: 0b0e3aa07c8c063ddf40b082bdf7e37a1562bda40a0ff5272957f3e987e0e54b
  url: https://github.com/lz4/lz4/archive/v1.9.4.tar.gz
build:
  number: '1'
  run_exports:
    - lz4-c >=1.9.4,<1.10.0a0
  string: h2bbff1b_1
requirements:
  build:
    - m2-gcc-libs 5.3.0 4
    - m2-msys2-runtime 2.5.0.17080.65c939c 3
    - msys2-conda-epoch 20160418 1
    - vs2017_win-64 19.16.27032.1 hb4161e2_3
    - vswhere 2.8.4 haa95532_0
  host:
    - vc 14.2 h21ff451_1
    - vs2015_runtime 14.27.29016 h5e58377_2
  run:
    - vc >=14.1,<15.0a0
    - vs2015_runtime >=14.16.27012,<15.0a0
about:
  description: 'LZ4 is lossless compression algorithm, providing compression speed
    at 400

    MB/s per core (0.16 Bytes/cycle). It features an extremely fast decoder,

    with speed in multiple GB/s per core (0.71 Bytes/cycle). A high compression

    derivative, called LZ4_HC, is available, trading customizable CPU time for

    compression ratio. LZ4 library is provided as open source software using a

    BSD license.

    '
  dev_url: https://github.com/lz4/lz4
  doc_url: https://github.com/lz4/lz4/blob/dev/README.md
  home: https://lz4.github.io/lz4/
  license: BSD-2-Clause
  license_family: BSD
  license_file: lib/LICENSE
  summary: Extremely Fast Compression algorithm
extra:
  copy_test_source_files: true
  final: true
  flow_run_id: fd236b1e-d58b-461e-afcc-d51a3b0a0080
  recipe-maintainers:
    - mingwandroid
    - rmax
    - wesm
    - xhochy
  remote_url: **************:AnacondaRecipes/lz4-c-feedstock.git
  sha: 45537caf4f5443bf05cd61697e4cc857567e2aee
