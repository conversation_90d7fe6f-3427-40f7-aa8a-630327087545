.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_sftp_stat_ex 3 "1 Jun 2007" "libssh2 0.15" "libssh2"
.SH NAME
libssh2_sftp_stat_ex - get status about an SFTP file
.SH SYNOPSIS
.nf
#include <libssh2.h>
#include <libssh2_sftp.h>

int
libssh2_sftp_stat_ex(LIBSSH2_SFTP *sftp, const char *path,
                     unsigned int path_len, int stat_type,
                     LIBSSH2_SFTP_ATTRIBUTES *attrs);
.fi
.SH DESCRIPTION
\fIsftp\fP - SFTP instance as returned by
.BR libssh2_sftp_init(3)

\fIpath\fP - Remote filesystem object to stat/lstat/setstat.

\fIpath_len\fP - Length of the name of the remote filesystem object
to stat/lstat/setstat.

\fIstat_type\fP - One of the three constants specifying the type of
stat operation to perform:

.br
\fBLIBSSH2_SFTP_STAT\fP: performs stat(2) operation
.br
\fBLIBSSH2_SFTP_LSTAT\fP: performs lstat(2) operation
.br
\fBLIBSSH2_SFTP_SETSTAT\fP: performs operation to set stat info on file

\fIattrs\fP - Pointer to a \fBLIBSSH2_SFTP_ATTRIBUTES\fP structure to set file
metadata from or into depending on the value of stat_type.

Get or Set statbuf type data on a remote filesystem object. When getting
statbuf data,
.BR libssh2_sftp_stat(3)
will follow all symlinks, while
.BR libssh2_sftp_lstat(3)
will return data about the object encountered, even if that object
happens to be a symlink.

The LIBSSH2_SFTP_ATTRIBUTES struct looks like this:

.nf
struct LIBSSH2_SFTP_ATTRIBUTES {
    /* If flags & ATTR_* bit is set, then the value in this struct will be
     * meaningful Otherwise it should be ignored
     */
    unsigned long flags;

    libssh2_uint64_t filesize;
    unsigned long uid;
    unsigned long gid;
    unsigned long permissions;
    unsigned long atime;
    unsigned long mtime;
};
.fi
.SH RETURN VALUE
Returns 0 on success or negative on failure. It returns LIBSSH2_ERROR_EAGAIN
when it would otherwise block. While LIBSSH2_ERROR_EAGAIN is a negative
number, it is not really a failure per se.
.SH ERRORS
\fILIBSSH2_ERROR_ALLOC\fP - An internal memory allocation call failed.

\fILIBSSH2_ERROR_SOCKET_SEND\fP - Unable to send data on socket.

\fILIBSSH2_ERROR_SOCKET_TIMEOUT\fP -

\fILIBSSH2_ERROR_SFTP_PROTOCOL\fP - An invalid SFTP protocol response was
received on the socket, or an SFTP operation caused an errorcode to
be returned by the server.
.SH SEE ALSO
.BR libssh2_sftp_init(3)
