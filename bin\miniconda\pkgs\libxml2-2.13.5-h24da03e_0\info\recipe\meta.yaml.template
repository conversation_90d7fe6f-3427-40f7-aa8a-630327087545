{% set version = "2.13.5" %}

package:
  name: libxml2
  version: {{ version }}

source:
  url: https://gitlab.gnome.org/GNOME/libxml2/-/archive/v{{ version }}/libxml2-v{{ version }}.tar.gz
  sha256: 37cdec8cd20af8ab0decfa2419b09b4337c2dbe9da5615d2a26f547449fecf2a
  patches:
    - 0002-Make-and-install-a-pkg-config-file-on-Windows.patch  # [win]

build:
  number: 0
  run_exports:
    # remove symbols at minor versions.
    #    https://abi-laboratory.pro/tracker/timeline/libxml2/
    - {{ pin_subpackage('libxml2', max_pin='x.x') }}

requirements:
  build:
    - {{ compiler('c') }}
    - autoconf   # [not win]
    - automake   # [not win]
    - libtool   # [not win]
    - pkg-config  # [not win]
    - make      # [not win]
    - m2-patch    # [win]
  host:
    - icu {{icu}}     # [not win]
    - libiconv 1.16   # [not linux]
    - xz {{xz}}       # [not win]
    - zlib {{zlib}}
  run:
    - libiconv  # [not linux]

test:
  files:
    - test.xml
  commands:
    - xmllint test.xml

about:
  home: https://gitlab.gnome.org/GNOME/libxml2/
  license: MIT
  license_family: MIT
  license_file: Copyright
  summary: The XML C parser and toolkit of Gnome
  description: |
     Though libxml2 is written in C a variety of language
     bindings make it available in other environments.
  doc_url: https://gitlab.gnome.org/GNOME/libxml2/-/wikis/home
  dev_url: https://gitlab.gnome.org/GNOME/libxml2/

extra:
  recipe-maintainers:
    - ocefpaf
    - jakirkham
    - mingwandroid
    - gillins
    - jschueller
    - msarahan
    - scopatz
    - chenghlee
