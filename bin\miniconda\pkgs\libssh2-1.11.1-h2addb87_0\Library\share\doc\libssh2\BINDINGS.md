libssh2 bindings
================

Creative people have written bindings or interfaces for various environments
and programming languages. Using one of these bindings allows you to take
advantage of libssh2 directly from within your favourite language.

The bindings listed below are not part of the libssh2 distribution archives,
but must be downloaded and installed separately.

<!-- markdown-link-check-disable -->

[Cocoa/Objective-C](https://github.com/karelia/libssh2_sftp-Cocoa-wrapper)

[Haskell FFI bindings](https://hackage.haskell.org/package/libssh2)

[Perl Net::SSH2](https://metacpan.org/pod/Net::SSH2)

[PHP ssh2](https://pecl.php.net/package/ssh2)

[Python pylibssh2](https://pypi.python.org/pypi/pylibssh2)

[Python-ctypes PySsh2](https://github.com/gellule/PySsh2)

[Ruby libssh2-ruby](https://github.com/mitchellh/libssh2-ruby)
