.\" Copyright (C) <PERSON>
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_knownhost_writefile 3 "28 May 2009" "libssh2" "libssh2"
.SH NAME
libssh2_knownhost_writefile - write a collection of known hosts to a file
.SH SYNOPSIS
.nf
#include <libssh2.h>

int
libssh2_knownhost_writefile(LIBSSH2_KNOWNHOSTS *hosts,
                            const char *filename, int type);
.fi
.SH DESCRIPTION
Writes all the known hosts to the specified file using the specified file
format.

\fIfilename\fP specifies what filename to create

\fItype\fP specifies what file type it is, and
\fILIBSSH2_KNOWNHOST_FILE_OPENSSH\fP is the only currently supported
format.
.SH RETURN VALUE
Returns a regular libssh2 error code, where negative values are error codes
and 0 indicates success.
.SH AVAILABILITY
Added in libssh2 1.2
.SH SEE ALSO
.BR libssh2_knownhost_readfile(3)
.BR libssh2_knownhost_add(3)
