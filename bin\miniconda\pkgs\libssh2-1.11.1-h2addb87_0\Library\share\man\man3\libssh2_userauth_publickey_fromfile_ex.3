.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_userauth_publickey_fromfile_ex 3 "1 Jun 2007" "libssh2 0.15" "libssh2"
.SH NAME
libssh2_userauth_publickey_fromfile_ex - authenticate a session with a public key, read from a file
.SH SYNOPSIS
.nf
#include <libssh2.h>

int
libssh2_userauth_publickey_fromfile_ex(LIBSSH2_SESSION *session,
                                       const char *username,
                                       unsigned int username_len,
                                       const char *publickey,
                                       const char *privatekey,
                                       const char *passphrase);
.fi
.SH DESCRIPTION
\fIsession\fP - Session instance as returned by
\fBlibssh2_session_init_ex(3)\fP

\fIusername\fP - Pointer to user name to authenticate as.

\fIusername_len\fP - Length of \fIusername\fP.

\fIpublickey\fP - Path name of the public key file.
(e.g. /etc/ssh/hostkey.pub). If libssh2 is built against OpenSSL, this option
can be set to NULL.

\fIprivatekey\fP - Path name of the private key file. (e.g. /etc/ssh/hostkey)

\fIpassphrase\fP - Passphrase to use when decoding \fIprivatekey\fP.

Attempt public key authentication using either a public key file or a PEM
encoded private key file stored on disk. When providing a private key, the
public key is automatically extracted from it. When providing both, the
passed public key takes precedence.
.SH RETURN VALUE
Return 0 on success or negative on failure. It returns
LIBSSH2_ERROR_EAGAIN when it would otherwise block. While
LIBSSH2_ERROR_EAGAIN is a negative number, it is not really a failure per se.
.SH ERRORS
\fILIBSSH2_ERROR_FILE\fP - An issue opening, reading or parsing the disk file.

\fILIBSSH2_ERROR_ALLOC\fP - An internal memory allocation call failed.

\fILIBSSH2_ERROR_SOCKET_SEND\fP - Unable to send data on socket.

\fILIBSSH2_ERROR_SOCKET_TIMEOUT\fP -

\fILIBSSH2_ERROR_PUBLICKEY_UNVERIFIED\fP - The username/public key
combination was invalid.

\fILIBSSH2_ERROR_AUTHENTICATION_FAILED\fP - Authentication using the supplied
public key was not accepted.
.SH SEE ALSO
.BR libssh2_session_init_ex(3)
