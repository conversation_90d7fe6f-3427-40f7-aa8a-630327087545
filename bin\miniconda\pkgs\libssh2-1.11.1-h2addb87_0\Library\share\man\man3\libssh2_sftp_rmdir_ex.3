.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_sftp_rmdir_ex 3 "1 Jun 2007" "libssh2 0.15" "libssh2"
.SH NAME
libssh2_sftp_rmdir_ex - remove an SFTP directory
.SH SYNOPSIS
.nf
#include <libssh2.h>
#include <libssh2_sftp.h>

int
libssh2_sftp_rmdir_ex(LIBSSH2_SFTP *sftp, const char *path,
                      unsigned int path_len);
.fi
.SH DESCRIPTION
Remove a directory from the remote file system.

\fIsftp\fP - SFTP instance as returned by
.BR libssh2_sftp_init(3)

\fIsourcefile\fP - Full path of the existing directory to remove.

\fIsourcefile_len\fP - Length of the full path of the existing directory to
remove.
.SH RETURN VALUE
Return 0 on success or negative on failure. It returns
LIBSSH2_ERROR_EAGAIN when it would otherwise block. While
LIBSSH2_ERROR_EAGAIN is a negative number, it is not really a failure per se.
.SH ERRORS
\fILIBSSH2_ERROR_ALLOC\fP - An internal memory allocation call failed.

\fILIBSSH2_ERROR_SOCKET_SEND\fP - Unable to send data on socket.

\fILIBSSH2_ERROR_SOCKET_TIMEOUT\fP -

\fILIBSSH2_ERROR_SFTP_PROTOCOL\fP - An invalid SFTP protocol response was
received on the socket, or an SFTP operation caused an errorcode to
be returned by the server.
.SH SEE ALSO
.BR libssh2_sftp_init(3)
