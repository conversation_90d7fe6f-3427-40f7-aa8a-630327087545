.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_agent_sign 3 "1 Oct 2022" "libssh2" "libssh2"
.SH NAME
libssh2_agent_sign - sign data, with the help of ssh-agent
.SH SYNOPSIS
.nf
#include <libssh2.h>

int
libssh2_agent_sign(LIBSSH2_AGENT *agent,
                   struct libssh2_agent_publickey *identity,
                   unsigned char **sig,
                   size_t *s_len,
                   const unsigned char *data,
                   size_t d_len,
                   const char *method,
                   unsigned int method_len);
.fi
.SH DESCRIPTION
\fIagent\fP - ssh-agent handle as returned by
.BR libssh2_agent_init(3)

\fIidentity\fP - Public key to authenticate with, as returned by
.BR libssh2_agent_get_identity(3)

\fIsig\fP - A pointer to a buffer in which to place the signature. The caller
is responsible for freeing the signature with LIBSSH2_FREE.

\fIs_len\fP - A pointer to the length of the sig parameter.

\fIdata\fP - The data to sign.

\fId_len\fP - The length of the data parameter.

\fImethod\fP - A buffer indicating the signing method. This should match the
string at the start of identity->blob.

\fImethod_len\fP - The length of the method parameter.

Sign data using an ssh-agent. This function can be used in a callback
registered with libssh2_session_callback_set2(3) using
LIBSSH2_CALLBACK_AUTHAGENT_SIGN to sign an authentication challenge from a
server. However, the client is responsible for implementing the code that calls
this callback in response to a SSH2_AGENTC_SIGN_REQUEST message.
.SH RETURN VALUE
Returns 0 if succeeded, or a negative value for error.
.SH AVAILABILITY
Added in libssh2 1.11.0
.SH SEE ALSO
.BR libssh2_agent_init(3)
.BR libssh2_agent_get_identity(3)
.BR libssh2_agent_userauth(3)
.BR libssh2_session_callback_set2(3)
