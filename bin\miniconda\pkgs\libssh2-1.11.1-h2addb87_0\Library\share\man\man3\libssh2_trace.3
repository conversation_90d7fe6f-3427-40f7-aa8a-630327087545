.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_trace 3 "26 Dec 2008" "libssh2 1.0" "libssh2"
.SH NAME
libssh2_trace - enable debug info from inside libssh2
.SH SYNOPSIS
.nf
#include <libssh2.h>

void
libssh2_trace(LIBSSH2_SESSION *session, int bitmask);
.fi
.SH DESCRIPTION
This is a function present in the library that can be used to get debug info
from within libssh2 when it is running. Helpful when trying to trace or debug
behaviors. Note that this function has no effect unless libssh2 was built to
support tracing! It is usually disabled in release builds.

\fBbitmask\fP can be set to the logical OR of none, one or more of these:
.RS
.IP LIBSSH2_TRACE_SOCKET
Socket low-level debugging
.IP LIBSSH2_TRACE_TRANS
Transport layer debugging
.IP LIBSSH2_TRACE_KEX
Key exchange debugging
.IP LIBSSH2_TRACE_AUTH
Authentication debugging
.IP LIBSSH2_TRACE_CONN
Connection layer debugging
.IP LIBSSH2_TRACE_SCP
SCP debugging
.IP LIBSSH2_TRACE_SFTP
SFTP debugging
.IP LIBSSH2_TRACE_ERROR
Error debugging
.IP LIBSSH2_TRACE_PUBLICKEY
Public Key debugging
.RE
