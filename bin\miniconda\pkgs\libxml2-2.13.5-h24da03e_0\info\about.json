{"channels": ["https://repo.anaconda.com/pkgs/main"], "conda_build_version": "24.9.0", "conda_version": "24.9.2", "description": "Though libxml2 is written in C a variety of language\nbindings make it available in other environments.\n", "dev_url": "https://gitlab.gnome.org/GNOME/libxml2/", "doc_url": "https://gitlab.gnome.org/GNOME/libxml2/-/wikis/home", "env_vars": {"CIO_TEST": "<not set>"}, "extra": {"copy_test_source_files": true, "final": true, "flow_run_id": "ec2a0bcd-4377-47a7-a8aa-d90d81bc4af5", "recipe-maintainers": ["ocefpaf", "<PERSON><PERSON><PERSON><PERSON>", "mingwandroid", "gillins", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "scopatz", "ch<PERSON><PERSON><PERSON>"], "remote_url": "**************:AnacondaRecipes/libxml2-feedstock.git", "sha": "26bac48fa7b8e3639653c9bcde27b7174ddc6ed5"}, "home": "https://gitlab.gnome.org/GNOME/libxml2/", "identifiers": [], "keywords": [], "license": "MIT", "license_family": "MIT", "license_file": "Copyright", "root_pkgs": ["abs-sdk 0.1.0+1553.gb553ace4 py_0", "aiobotocore 2.12.3 py39haa95532_0", "aiohappyeyeballs 2.4.0 py39haa95532_0", "aiohttp 3.10.5 py39h827c3e9_0", "aioitertools 0.7.1 pyhd3eb1b0_0", "aiosignal 1.2.0 pyhd3eb1b0_0", "anaconda-anon-usage 0.4.4 py39hfc23b7f_100", "anaconda-channel-scanner 1.3.0 py_1", "anaconda-client 1.12.3 py39haa95532_0", "annotated-types 0.6.0 py39haa95532_0", "archspec 0.2.3 pyhd3eb1b0_0", "async-timeout 4.0.3 py39haa95532_0", "attrs 24.2.0 py39haa95532_0", "bcrypt 3.2.0 py39h2bbff1b_1", "beautifulsoup4 4.12.3 py39haa95532_0", "boltons 23.0.0 py39haa95532_0", "boto3 1.34.69 py39haa95532_0", "botocore 1.34.69 py39haa95532_0", "brotli-python 1.0.9 py39hd77b12b_8", "bzip2 1.0.8 h2bbff1b_6", "ca-certificates 2024.9.24 haa95532_0", "certifi 2024.8.30 py39haa95532_0", "cffi 1.17.1 py39h827c3e9_0", "chardet 4.0.0 py39haa95532_1003", "charset-normalizer 3.3.2 pyhd3eb1b0_0", "click 8.1.7 py39haa95532_0", "cloudpickle 3.0.0 py39haa95532_0", "colorama 0.4.6 py39haa95532_0", "conda 24.9.2 py39haa95532_0", "conda-build 24.9.0 py39haa95532_0", "conda-content-trust 0.2.0 py39haa95532_1", "conda-index 0.5.0 py39haa95532_0", "conda-libmamba-solver 24.9.0 pyhd3eb1b0_0", "conda-package-handling 2.3.0 py39haa95532_0", "conda-package-streaming 0.10.0 py39haa95532_0", "console_shortcut_miniconda 0.1.1 haa95532_2", "croniter 0.3.35 py_0", "cryptography 43.0.0 py39h89fc84f_0", "dask-core 2024.5.0 py39haa95532_0", "datadog 0.42.0 pyhd3eb1b0_0", "defusedxml 0.7.1 pyhd3eb1b0_0", "deprecated 1.2.13 py39haa95532_0", "distributed 2024.5.0 py39haa95532_0", "distro 1.9.0 py39haa95532_0", "docker-py 7.0.0 py39haa95532_0", "filelock 3.13.1 py39haa95532_0", "fmt 9.1.0 h6d14046_1", "frozendict 2.4.2 py39h2bbff1b_0", "frozenlist 1.5.0 py39h827c3e9_0", "fsspec 2024.6.1 py39haa95532_0", "gitdb 4.0.7 pyhd3eb1b0_0", "gitpython 3.1.43 py39haa95532_0", "heapdict 1.0.1 pyhd3eb1b0_0", "idna 3.7 py39haa95532_0", "importlib-metadata 7.0.1 py39haa95532_0", "importlib_resources 6.4.0 py39haa95532_0", "jinja2 3.1.4 py39haa95532_1", "jmespath 1.0.1 py39haa95532_0", "jsonpatch 1.33 py39haa95532_1", "jsonpointer 2.1 pyhd3eb1b0_0", "jsonschema 4.23.0 py39haa95532_0", "jsonschema-specifications 2023.7.1 py39haa95532_0", "jupyter_core 5.7.2 py39haa95532_0", "libarchive 3.7.4 h9243413_0", "libcurl 8.9.1 h0416ee5_0", "libiconv 1.16 h2bbff1b_3", "liblief 0.12.3 hd77b12b_0", "libmamba 1.5.8 h99b1521_3", "libmambapy 1.5.8 py39h77c03ed_3", "libsolv 0.7.24 h23ce68f_1", "libssh2 1.11.0 h291bd65_0", "libxml2 2.13.1 h24da03e_2", "locket 1.0.0 py39haa95532_0", "lz4-c 1.9.4 h2bbff1b_1", "m2-msys2-runtime 2.5.0.17080.65c939c 3", "m2-patch 2.7.5 2", "markupsafe 2.1.3 py39h2bbff1b_0", "marshmallow 3.19.0 py39haa95532_0", "marshmallow-oneofschema 3.0.1 py39haa95532_0", "menuinst 2.1.2 py39h5da7b33_0", "more-itertools 10.3.0 py39haa95532_0", "msgpack-python 1.0.3 py39h59b6b97_0", "msys2-conda-epoch 20160418 1", "multidict 6.1.0 py39h827c3e9_0", "mypy_extensions 1.0.0 py39haa95532_0", "natsort 7.1.1 pyhd3eb1b0_0", "nbformat 5.10.4 py39haa95532_0", "openssl 3.0.15 h827c3e9_0", "packaging 24.1 py39haa95532_0", "paramiko 2.8.1 pyhd3eb1b0_0", "partd 1.4.1 py39haa95532_0", "pcre2 10.42 h0ff8eda_1", "pendulum 2.1.2 pyhd3eb1b0_1", "percy 0.1.0 pyhd3eb1b0_0", "pip 24.2 py39haa95532_0", "pkginfo 1.10.0 py39haa95532_0", "platformdirs 3.10.0 py39haa95532_0", "pluggy 1.0.0 py39haa95532_1", "powershell_shortcut_miniconda 0.0.1 haa95532_2", "prefect 1.4.1 pyhd3eb1b0_0", "psutil 5.9.0 py39h2bbff1b_0", "py-lief 0.12.3 py39hd77b12b_0", "pybind11-abi 5 hd3eb1b0_0", "pycosat 0.6.6 py39h2bbff1b_1", "pycparser 2.21 pyhd3eb1b0_0", "pydantic 2.8.2 py39haa95532_0", "pydantic-core 2.20.1 py39hefb1915_0", "pygithub 2.4.0 py39haa95532_0", "pyjwt 2.9.0 py39haa95532_0", "pynacl 1.5.0 py39h8cc25b3_0", "pyopenssl 24.2.1 py39haa95532_0", "pysocks 1.7.1 py39haa95532_0", "python 3.9.18 h1aa4202_0", "python-box 5.4.1 pyhd3eb1b0_0", "python-dateutil 2.9.0post0 py39haa95532_2", "python-fastjsonschema 2.16.2 py39haa95532_0", "python-json-logger 2.0.7 py39haa95532_0", "python-libarchive-c 5.1 pyhd3eb1b0_0", "python-lmdb 1.4.1 py39hd77b12b_0", "python-slugify 5.0.2 pyhd3eb1b0_0", "pytz 2024.1 py39haa95532_0", "pytzdata 2020.1 pyhd3eb1b0_0", "pywin32 305 py39h2bbff1b_0", "pyyaml 6.0.2 py39h827c3e9_0", "referencing 0.30.2 py39haa95532_0", "reproc 14.2.4 hd77b12b_2", "reproc-cpp 14.2.4 hd77b12b_2", "requests 2.32.3 py39haa95532_1", "requests-toolbelt 1.0.0 py39haa95532_0", "rpds-py 0.10.6 py39h062c2fa_0", "ruamel.yaml 0.18.6 py39h827c3e9_0", "ruamel.yaml.clib 0.2.8 py39h827c3e9_0", "s3transfer 0.10.1 py39haa95532_0", "setuptools 75.1.0 py39haa95532_0", "six 1.16.0 pyhd3eb1b0_1", "slack-sdk 3.19.5 pyhaa95532_0", "smmap 4.0.0 pyhd3eb1b0_0", "sortedcontainers 2.4.0 pyhd3eb1b0_0", "soupsieve 2.5 py39haa95532_0", "sqlite 3.45.3 h2bbff1b_0", "tabulate 0.9.0 py39haa95532_0", "tblib 1.7.0 pyhd3eb1b0_0", "termcolor 2.1.0 py39haa95532_0", "text-unidecode 1.3 pyhd3eb1b0_0", "toml 0.10.2 pyhd3eb1b0_0", "tomli 2.0.1 py39haa95532_0", "toolz 0.12.0 py39haa95532_0", "tornado 6.4.1 py39h827c3e9_0", "tqdm 4.66.5 py39h9909e9c_0", "traitlets 5.14.3 py39haa95532_0", "typing-extensions 4.11.0 py39haa95532_0", "typing_extensions 4.11.0 py39haa95532_0", "tzdata 2024b h04d1e81_0", "unidecode 1.3.8 py39haa95532_0", "urllib3 1.26.19 py39haa95532_0", "vc 14.40 h2eaa2aa_1", "vs2015_runtime 14.40.33807 h98bb1dd_1", "websocket-client 1.8.0 py39haa95532_0", "wheel 0.44.0 py39haa95532_0", "win_inet_pton 1.1.0 py39haa95532_0", "wrapt 1.14.1 py39h2bbff1b_0", "xz 5.4.6 h8cc25b3_1", "yaml 0.2.5 he774522_0", "yaml-cpp 0.8.0 hd77b12b_1", "yarl 1.11.0 py39h827c3e9_0", "zict 3.0.0 py39haa95532_0", "zipp 3.20.2 py39haa95532_0", "zlib 1.2.13 h8cc25b3_1", "zstandard 0.23.0 py39h4fc1ca9_0", "zstd 1.5.6 h8880b57_0"], "summary": "The XML C parser and toolkit of Gnome", "tags": []}