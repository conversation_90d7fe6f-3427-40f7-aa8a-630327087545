menuinst-2.2.0.dist-info/AUTHORS.md,sha256=yS2zzHka7igL8t7n7GJptvCDWVx19LL0izxVI1hyD_Q,638
menuinst-2.2.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
menuinst-2.2.0.dist-info/LICENSE.txt,sha256=kRtpO7NElNtr0sJPWRZ0Yz_pm4vBRCvD_PUSGgqC-uU,1530
menuinst-2.2.0.dist-info/METADATA,sha256=WOcWW3mDMXAbk9YFA3IDyM95V3LIPZS_gE-KeK-MppQ,4915
menuinst-2.2.0.dist-info/RECORD,,
menuinst-2.2.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
menuinst-2.2.0.dist-info/WHEEL,sha256=rzGfZgUcGeKSgIHGYMuqg4xE4VPHxnaldXH6BG0zjVk,101
menuinst-2.2.0.dist-info/direct_url.json,sha256=FV8Z0WfNJHD2qLOWSgWfMOVesZGJqLHxRwzrc0dKXds,88
menuinst-2.2.0.dist-info/top_level.txt,sha256=9vHGmIUVj52s7Jka1ETdtAG9LWRCMqX5FRrlOR3nhz4,9
menuinst/__init__.py,sha256=VwRtbWQSTz2P4kJ_--viEr0pJRWjLyd1aLsltk8rFGA,1537
menuinst/__pycache__/__init__.cpython-310.pyc,,
menuinst/__pycache__/_schema.cpython-310.pyc,,
menuinst/__pycache__/_version.cpython-310.pyc,,
menuinst/__pycache__/api.cpython-310.pyc,,
menuinst/__pycache__/utils.cpython-310.pyc,,
menuinst/_legacy/__init__.py,sha256=1g1X6AhfTf319VvNE8k2fHkw8GqYjS4rnl2oXkMQG54,2879
menuinst/_legacy/__pycache__/__init__.cpython-310.pyc,,
menuinst/_legacy/__pycache__/cwp.cpython-310.pyc,,
menuinst/_legacy/__pycache__/main.cpython-310.pyc,,
menuinst/_legacy/__pycache__/utils.cpython-310.pyc,,
menuinst/_legacy/__pycache__/win32.cpython-310.pyc,,
menuinst/_legacy/cwp.py,sha256=E3wnjr69seK1mT2MC1kC4q4YQlyBYnX0XFs6ebFcR-w,1873
menuinst/_legacy/main.py,sha256=IdzwxgNWrMyCBjp_HzxFJWgVOdenANvueg6yso6vCLY,693
menuinst/_legacy/utils.py,sha256=ZUdorwo1itv43wdLADyVTP36iIB3zgodYirf4DS6ztI,522
menuinst/_legacy/win32.py,sha256=SMbC-Ze6OnwBfBOr2Ub9l9e4dZnb3Nvxw3gNhFg-Y6M,11169
menuinst/_schema.py,sha256=l91TUc-a_VtzZcqM85Q7guMuk_FblhNJqEmRspKF-Ew,19247
menuinst/_vendor/apipkg/LICENSE,sha256=6J7tEHTTqUMZi6E5uAhE9bRFuGC7p0qK6twGEFZhZOo,1054
menuinst/_vendor/apipkg/__init__.py,sha256=TSNnn3vnuhvO4HbB6f3e4KjpVxkHyVTe-RFiQ7-LZ6U,1123
menuinst/_vendor/apipkg/__pycache__/__init__.cpython-310.pyc,,
menuinst/_vendor/apipkg/__pycache__/_alias_module.cpython-310.pyc,,
menuinst/_vendor/apipkg/__pycache__/_importing.cpython-310.pyc,,
menuinst/_vendor/apipkg/__pycache__/_module.cpython-310.pyc,,
menuinst/_vendor/apipkg/__pycache__/_syncronized.cpython-310.pyc,,
menuinst/_vendor/apipkg/__pycache__/_version.cpython-310.pyc,,
menuinst/_vendor/apipkg/_alias_module.py,sha256=Ga4pGUXe_UFzc4ItrNmYMUWc9GC4Ytv5sHN9QPnaWDY,1186
menuinst/_vendor/apipkg/_importing.py,sha256=Jf7URncIVWn_UBY5d1Va8-5U9S7foAezSmdWx4HAzPo,1067
menuinst/_vendor/apipkg/_module.py,sha256=vV36vRZUpgBO_o5wR24ufTCM_CCqKZPGZA6P3FwH1dE,6897
menuinst/_vendor/apipkg/_syncronized.py,sha256=1Gv-iyKS7tMeHta6VdYrJ7jyMVU4BAP-90WidLgnpXc,484
menuinst/_vendor/apipkg/_version.py,sha256=A7pe6wMfnFjTxQsnriLV8_JdbbuBkrN3VnyK-lS-TBA,142
menuinst/_vendor/apipkg/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
menuinst/_version.py,sha256=K6n4zrDcbTiVSc7RJ0JOH4gMox5RqVkoCZUHlEl9LrU,427
menuinst/api.py,sha256=OlqP6cin9_y9ZWjZQpQR-UB9C5WraSsVc4maOR2LYTI,5566
menuinst/data/menuinst.default.json,sha256=xFb25YhliojqoLAQZ7hSPM7WNb_2RLC1AXuQQwLp7M0,2000
menuinst/data/menuinst.schema.json,sha256=8iOokaDpHXqVBnMSsZro741kYosy91T_C67x14We3WY,19053
menuinst/platforms/__init__.py,sha256=Ks65y_d5_SueAjwG28hh9asBZiAFKq7P3b5MfPMdqBE,744
menuinst/platforms/__pycache__/__init__.cpython-310.pyc,,
menuinst/platforms/__pycache__/base.cpython-310.pyc,,
menuinst/platforms/__pycache__/linux.cpython-310.pyc,,
menuinst/platforms/__pycache__/osx.cpython-310.pyc,,
menuinst/platforms/__pycache__/win.cpython-310.pyc,,
menuinst/platforms/base.py,sha256=I7IbsykwZNPfygT9FiVG1n6EK6M6I5hfXZAZsvNPKwc,9112
menuinst/platforms/linux.py,sha256=L2W1E4J6xN1bMBGNyhA2dq-CaB0BK9zUcrZO-Ig_CiI,16210
menuinst/platforms/osx.py,sha256=jpUTKZxESMmWWzCBn5Qv12FHEDiUC-Q1fTqDsVdeYxY,13253
menuinst/platforms/win.py,sha256=GvJQ_oXfrUKo4CxF1CdZTAkJPpJt54aeszMVEQ6ERR4,20081
menuinst/platforms/win_utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
menuinst/platforms/win_utils/__pycache__/__init__.cpython-310.pyc,,
menuinst/platforms/win_utils/__pycache__/knownfolders.cpython-310.pyc,,
menuinst/platforms/win_utils/__pycache__/registry.cpython-310.pyc,,
menuinst/platforms/win_utils/__pycache__/win_elevate.cpython-310.pyc,,
menuinst/platforms/win_utils/knownfolders.py,sha256=ZGB-k2IexNOk2K-yRCGMF5HzxtS-GqG-lILbaU29sSk,15303
menuinst/platforms/win_utils/registry.py,sha256=b9aae_i27VtEmRBoMIKSSQ6fB-hC0NDbHwU4lfYcTLI,7872
menuinst/platforms/win_utils/win_elevate.py,sha256=9BkZEK8y4xPVlkrOvjEeHmTLd7fF9CCjSOTluj280C0,5068
menuinst/platforms/win_utils/winshortcut.cp310-win_amd64.pyd,sha256=bndHCcpniY_JZxzYIZxt925DpUviAF0CMSEPxotpDi0,13312
menuinst/utils.py,sha256=QyozeuQINmMK3FosbOedaPmTxnx_ZS_W2eaZxud-L5Y,17837
