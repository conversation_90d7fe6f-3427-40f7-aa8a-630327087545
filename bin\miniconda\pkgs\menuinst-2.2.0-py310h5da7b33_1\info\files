Lib/site-packages/menuinst-2.2.0.dist-info/AUTHORS.md
Lib/site-packages/menuinst-2.2.0.dist-info/INSTALLER
Lib/site-packages/menuinst-2.2.0.dist-info/LICENSE.txt
Lib/site-packages/menuinst-2.2.0.dist-info/METADATA
Lib/site-packages/menuinst-2.2.0.dist-info/RECORD
Lib/site-packages/menuinst-2.2.0.dist-info/REQUESTED
Lib/site-packages/menuinst-2.2.0.dist-info/WHEEL
Lib/site-packages/menuinst-2.2.0.dist-info/direct_url.json
Lib/site-packages/menuinst-2.2.0.dist-info/top_level.txt
Lib/site-packages/menuinst/__init__.py
Lib/site-packages/menuinst/__pycache__/__init__.cpython-310.pyc
Lib/site-packages/menuinst/__pycache__/_schema.cpython-310.pyc
Lib/site-packages/menuinst/__pycache__/_version.cpython-310.pyc
Lib/site-packages/menuinst/__pycache__/api.cpython-310.pyc
Lib/site-packages/menuinst/__pycache__/utils.cpython-310.pyc
Lib/site-packages/menuinst/_legacy/__init__.py
Lib/site-packages/menuinst/_legacy/__pycache__/__init__.cpython-310.pyc
Lib/site-packages/menuinst/_legacy/__pycache__/cwp.cpython-310.pyc
Lib/site-packages/menuinst/_legacy/__pycache__/main.cpython-310.pyc
Lib/site-packages/menuinst/_legacy/__pycache__/utils.cpython-310.pyc
Lib/site-packages/menuinst/_legacy/__pycache__/win32.cpython-310.pyc
Lib/site-packages/menuinst/_legacy/cwp.py
Lib/site-packages/menuinst/_legacy/main.py
Lib/site-packages/menuinst/_legacy/utils.py
Lib/site-packages/menuinst/_legacy/win32.py
Lib/site-packages/menuinst/_schema.py
Lib/site-packages/menuinst/_vendor/apipkg/LICENSE
Lib/site-packages/menuinst/_vendor/apipkg/__init__.py
Lib/site-packages/menuinst/_vendor/apipkg/__pycache__/__init__.cpython-310.pyc
Lib/site-packages/menuinst/_vendor/apipkg/__pycache__/_alias_module.cpython-310.pyc
Lib/site-packages/menuinst/_vendor/apipkg/__pycache__/_importing.cpython-310.pyc
Lib/site-packages/menuinst/_vendor/apipkg/__pycache__/_module.cpython-310.pyc
Lib/site-packages/menuinst/_vendor/apipkg/__pycache__/_syncronized.cpython-310.pyc
Lib/site-packages/menuinst/_vendor/apipkg/__pycache__/_version.cpython-310.pyc
Lib/site-packages/menuinst/_vendor/apipkg/_alias_module.py
Lib/site-packages/menuinst/_vendor/apipkg/_importing.py
Lib/site-packages/menuinst/_vendor/apipkg/_module.py
Lib/site-packages/menuinst/_vendor/apipkg/_syncronized.py
Lib/site-packages/menuinst/_vendor/apipkg/_version.py
Lib/site-packages/menuinst/_vendor/apipkg/py.typed
Lib/site-packages/menuinst/_version.py
Lib/site-packages/menuinst/api.py
Lib/site-packages/menuinst/data/menuinst.default.json
Lib/site-packages/menuinst/data/menuinst.schema.json
Lib/site-packages/menuinst/platforms/__init__.py
Lib/site-packages/menuinst/platforms/__pycache__/__init__.cpython-310.pyc
Lib/site-packages/menuinst/platforms/__pycache__/base.cpython-310.pyc
Lib/site-packages/menuinst/platforms/__pycache__/linux.cpython-310.pyc
Lib/site-packages/menuinst/platforms/__pycache__/osx.cpython-310.pyc
Lib/site-packages/menuinst/platforms/__pycache__/win.cpython-310.pyc
Lib/site-packages/menuinst/platforms/base.py
Lib/site-packages/menuinst/platforms/linux.py
Lib/site-packages/menuinst/platforms/osx.py
Lib/site-packages/menuinst/platforms/win.py
Lib/site-packages/menuinst/platforms/win_utils/__init__.py
Lib/site-packages/menuinst/platforms/win_utils/__pycache__/__init__.cpython-310.pyc
Lib/site-packages/menuinst/platforms/win_utils/__pycache__/knownfolders.cpython-310.pyc
Lib/site-packages/menuinst/platforms/win_utils/__pycache__/registry.cpython-310.pyc
Lib/site-packages/menuinst/platforms/win_utils/__pycache__/win_elevate.cpython-310.pyc
Lib/site-packages/menuinst/platforms/win_utils/knownfolders.py
Lib/site-packages/menuinst/platforms/win_utils/registry.py
Lib/site-packages/menuinst/platforms/win_utils/win_elevate.py
Lib/site-packages/menuinst/platforms/win_utils/winshortcut.cp310-win_amd64.pyd
Lib/site-packages/menuinst/utils.py
cwp.py
