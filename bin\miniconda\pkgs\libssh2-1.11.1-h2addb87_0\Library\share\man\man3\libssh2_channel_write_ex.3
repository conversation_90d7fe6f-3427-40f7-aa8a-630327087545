.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_channel_write_ex 3 "1 Jun 2007" "libssh2 0.15" "libssh2"
.SH NAME
libssh2_channel_write_ex - write data to a channel stream blocking
.SH SYNOPSIS
.nf
#include <libssh2.h>

ssize_t
libssh2_channel_write_ex(LIBSSH2_CHANNEL *channel,
                         int stream_id, char *buf,
                         size_t buflen);
.fi
.SH DESCRIPTION
Write data to a channel stream. All channel streams have one standard I/O
substream (stream_id == 0), and may have up to 2^32 extended data streams as
identified by the selected \fIstream_id\fP. The SSH2 protocol currently
defines a stream ID of 1 to be the stderr substream.

\fIchannel\fP - active channel stream to write to.

\fIstream_id\fP - substream ID number (e.g. 0 or SSH_EXTENDED_DATA_STDERR)

\fIbuf\fP - pointer to buffer to write

\fIbuflen\fP - size of the data to write

\fIlibssh2_channel_write(3)\fP and \fIlibssh2_channel_write_stderr(3)\fP are
convenience macros for this function.

\fIlibssh2_channel_write_ex(3)\fP will use as much as possible of the buffer
and put it into a single SSH protocol packet. This means that to get maximum
performance when sending larger files, you should try to always pass in at
least 32K of data to this function.
.SH RETURN VALUE
Actual number of bytes written or negative on failure.
LIBSSH2_ERROR_EAGAIN when it would otherwise block. While
LIBSSH2_ERROR_EAGAIN is a negative number, it is not really a failure per se.
.SH ERRORS
\fILIBSSH2_ERROR_ALLOC\fP - An internal memory allocation call failed.

\fILIBSSH2_ERROR_SOCKET_SEND\fP - Unable to send data on socket.

\fILIBSSH2_ERROR_CHANNEL_CLOSED\fP - The channel has been closed.

\fILIBSSH2_ERROR_CHANNEL_EOF_SENT\fP - The channel has been requested to be

\fILIBSSH2_ERROR_BAD_USE\fP - This can be returned if you ignored a previous
return for LIBSSH2_ERROR_EAGAIN and rather than sending the original buffer with
the original size, you sent a new buffer with a different size.

closed.
.SH SEE ALSO
.BR libssh2_channel_open_ex(3)
.BR libssh2_channel_read_ex(3)
