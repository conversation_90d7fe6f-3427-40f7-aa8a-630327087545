######################################################################
#
# group: undefined
# id: 4ca74734-6e9b-443c-ba64-370740094b5e
# index: 0
# cmd: reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled
# timestamp: 7/26/2025, 10:01:28 PM (1753592488820)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled

HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\FileSystem
    LongPathsEnabled    REG_DWORD    0x1


C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 1afd1a99-729e-46de-a7d1-7ba3122a98f1
# index: 0
# cmd: dir
# timestamp: 7/26/2025, 10:01:30 PM (1753592490061)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  913,137,815,552 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 88bbed65-7c53-48f0-85c7-e6fe40780089
# index: 0
# cmd: dir
# timestamp: 7/27/2025, 7:23:15 AM (1753626195815)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  913,112,289,280 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 21224fc8-785e-4598-8840-db9f31507c8a
# index: 0
# cmd: start /wait installer.exe /InstallationType=JustMe /RegisterPython=0 /S /D=C:\pinokio\bin\miniconda
# timestamp: 7/27/2025, 7:25:49 AM (1753626349086)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>start /wait installer.exe /InstallationType=JustMe /RegisterPython=0 /S /D=C:\pinokio\bin\miniconda
Welcome to Miniconda3 py310_25.1.1-2

By continuing this installation you are accepting this license agreement:
C:\pinokio\bin\miniconda\EULA.txt
Please run the installer in GUI mode to read the details.

Miniconda3 will now be installed into this location:
C:\pinokio\bin\miniconda

Unpacking payload...
Setting up the package cache...
Setting up the base environment...
Installing packages for base, creating shortcuts if necessary...
Initializing conda directories...
Running post install...
Done!

C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 90131b7b-22c8-4abd-b89a-5afbef864ff9
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 7/27/2025, 7:25:55 AM (1753626355002)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 82 (63.2 MB) tarball(s).
Will remove 1 index cache(s).
Will remove 3 (102 KB) package(s).
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 8f05892b-9e08-40a7-9461-bb3538077d14
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge sqlite=3.47.2
# timestamp: 7/27/2025, 7:27:02 AM (1753626422586)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge sqlite=3.47.2
Retrieving notices: done
Channels:
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done


==> WARNING: A newer version of conda exists. <==
    current version: 25.1.1
    latest version: 25.5.1

Please update conda by running

    $ conda update -n base -c defaults conda



## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - sqlite=3.47.2


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    ca-certificates-2025.7.14  |       h4c7d964_0         152 KB  conda-forge
    certifi-2025.7.14          |     pyhd8ed1ab_0         156 KB  conda-forge
    conda-25.5.1               |  py310h5588dad_0         924 KB  conda-forge
    libsqlite-3.47.2           |       h67fdade_0         870 KB  conda-forge
    openssl-3.5.1              |       h725018a_0         8.9 MB  conda-forge
    python_abi-3.10            |          2_cp310           4 KB  conda-forge
    sqlite-3.47.2              |       h2466b09_0         894 KB  conda-forge
    ucrt-10.0.22621.0          |       h57928b3_1         547 KB  conda-forge
    vc14_runtime-14.44.35208   |      h818238b_30         737 KB  conda-forge
    vs2015_runtime-14.44.35208 |      h38c0c73_30          17 KB  conda-forge
    ------------------------------------------------------------
                                           Total:        13.1 MB

The following NEW packages will be INSTALLED:

  libsqlite          conda-forge/win-64::libsqlite-3.47.2-h67fdade_0 
  python_abi         conda-forge/win-64::python_abi-3.10-2_cp310 
  ucrt               conda-forge/win-64::ucrt-10.0.22621.0-h57928b3_1 
  vc14_runtime       conda-forge/win-64::vc14_runtime-14.44.35208-h818238b_30 

The following packages will be UPDATED:

  ca-certificates    pkgs/main/win-64::ca-certificates-202~ --> conda-forge/noarch::ca-certificates-2025.7.14-h4c7d964_0 
  certifi            pkgs/main/win-64::certifi-2025.1.31-p~ --> conda-forge/noarch::certifi-2025.7.14-pyhd8ed1ab_0 
  conda              pkgs/main::conda-25.1.1-py310haa95532~ --> conda-forge::conda-25.5.1-py310h5588dad_0 
  openssl              pkgs/main::openssl-3.0.15-h827c3e9_0 --> conda-forge::openssl-3.5.1-h725018a_0 
  sqlite                pkgs/main::sqlite-3.45.3-h2bbff1b_0 --> conda-forge::sqlite-3.47.2-h2466b09_0 
  vs2015_runtime     pkgs/main::vs2015_runtime-14.42.34433~ --> conda-forge::vs2015_runtime-14.44.35208-h38c0c73_30 



Downloading and Extracting Packages:

Preparing transaction: done 
Verifying transaction: done 
Executing transaction: done 
 
(base) C:\pinokio\bin> 
 
 
 


######################################################################
#
# group: undefined
# id: 8e07b681-c86d-4d59-9b97-f5742fcf0d80
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/27/2025, 7:27:06 AM (1753626426321)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# packages in environment at C:\pinokio\bin\miniconda:
#
# Name                        Version          Build              Channel
anaconda-anon-usage           0.5.0            py310hfc23b7f_100
anaconda_powershell_prompt    1.1.0            haa95532_0
anaconda_prompt               1.1.0            haa95532_0
annotated-types               0.6.0            py310haa95532_0
archspec                      0.2.3            pyhd3eb1b0_0
boltons                       24.1.0           py310haa95532_0
brotli-python                 1.0.9            py310h5da7b33_9
bzip2                         1.0.8            h2bbff1b_6
ca-certificates               2025.7.14        h4c7d964_0         conda-forge
certifi                       2025.7.14        pyhd8ed1ab_0       conda-forge
cffi                          1.17.1           py310h827c3e9_1
charset-normalizer            3.3.2            pyhd3eb1b0_0
colorama                      0.4.6            py310haa95532_0
conda                         25.5.1           py310h5588dad_0    conda-forge
conda-anaconda-telemetry      0.1.2            py310haa95532_0
conda-anaconda-tos            0.1.2            py310haa95532_0
conda-content-trust           0.2.0            py310haa95532_1
conda-libmamba-solver         25.1.1           pyhd3eb1b0_0
conda-package-handling        2.4.0            py310haa95532_0
conda-package-streaming       0.11.0           py310haa95532_0
cpp-expected                  1.1.0            h214f63a_0
cryptography                  43.0.3           py310hbd6ee87_1
distro                        1.9.0            py310haa95532_0
fmt                           9.1.0            h6d14046_1
frozendict                    2.4.2            py310h2bbff1b_0
idna                          3.7              py310haa95532_0
jsonpatch                     1.33             py310haa95532_1
jsonpointer                   2.1              pyhd3eb1b0_0
libarchive                    3.7.7            h9243413_0
libcurl                       8.11.1           haff574d_0
libffi                        3.4.4            hd77b12b_1
libiconv                      1.16             h2bbff1b_3
libmamba                      2.0.5            hcd6fe79_1
libmambapy                    2.0.5            py310h214f63a_1
libsolv                       0.7.30           hf2fb9eb_1
libsqlite                     3.47.2           h67fdade_0         conda-forge
libssh2                       1.11.1           h2addb87_0
libxml2                       2.13.5           h24da03e_0
lz4-c                         1.9.4            h2bbff1b_1
markdown-it-py                2.2.0            py310haa95532_1
mdurl                         0.1.0            py310haa95532_0
menuinst                      2.2.0            py310h5da7b33_1
nlohmann_json                 3.11.2           h6c2663c_0
openssl                       3.5.1            h725018a_0         conda-forge
packaging                     24.2             py310haa95532_0
pcre2                         10.42            h0ff8eda_1
pip                           25.0             py310haa95532_0
platformdirs                  3.10.0           py310haa95532_0
pluggy                        1.5.0            py310haa95532_0
pybind11-abi                  5                hd3eb1b0_0
pycosat                       0.6.6            py310h827c3e9_2
pycparser                     2.21             pyhd3eb1b0_0
pydantic                      2.10.3           py310haa95532_0
pydantic-core                 2.27.1           py310h636fa0f_0
pygments                      2.15.1           py310haa95532_1
pysocks                       1.7.1            py310haa95532_0
python                        3.10.16          h4607a30_1
python_abi                    3.10             2_cp310            conda-forge
reproc                        14.2.4           hd77b12b_2
reproc-cpp                    14.2.4           hd77b12b_2
requests                      2.32.3           py310haa95532_1
rich                          13.9.4           py310haa95532_0
ruamel.yaml                   0.18.6           py310h827c3e9_0
ruamel.yaml.clib              0.2.8            py310h827c3e9_0
setuptools                    75.8.0           py310haa95532_0
simdjson                      3.10.1           h214f63a_0
spdlog                        1.11.0           h59b6b97_0
sqlite                        3.47.2           h2466b09_0         conda-forge
tk                            8.6.14           h0416ee5_0
tqdm                          4.67.1           py310h9909e9c_0
truststore                    0.10.0           py310haa95532_0
typing-extensions             4.12.2           py310haa95532_0
typing_extensions             4.12.2           py310haa95532_0
tzdata                        2025a            h04d1e81_0
ucrt                          10.0.22621.0     h57928b3_1         conda-forge
urllib3                       2.3.0            py310haa95532_0
vc                            14.42            haa95532_4
vc14_runtime                  14.44.35208      h818238b_30        conda-forge
vs2015_runtime                14.44.35208      h38c0c73_30        conda-forge
wheel                         0.45.1           py310haa95532_0
win_inet_pton                 1.1.0            py310haa95532_0
xz                            5.4.6            h8cc25b3_1
yaml-cpp                      0.8.0            hd77b12b_1
zlib                          1.2.13           h8cc25b3_1
zstandard                     0.23.0           py310h4fc1ca9_1
zstd                          1.5.6            h8880b57_0

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 1ce12a40-eb2c-48ed-b15f-4da411b65377
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 7/27/2025, 7:27:10 AM (1753626430255)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 10 (13.1 MB) tarball(s).
Will remove 1 index cache(s).
Will remove 8 (47.8 MB) package(s).
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: a0db9287-8b17-4b0b-bd6f-e36893d44921
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install --no-shortcuts -y -c conda-forge git git-lfs
# timestamp: 7/27/2025, 7:29:41 AM (1753626581517)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install --no-shortcuts -y -c conda-forge git git-lfs
Channels:
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - git
    - git-lfs


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    git-2.49.0                 |       h57928b3_2       121.6 MB  conda-forge
    git-lfs-3.7.0              |       h86e1c39_0         3.9 MB  conda-forge
    ------------------------------------------------------------
                                           Total:       125.5 MB

The following NEW packages will be INSTALLED:

  git                conda-forge/win-64::git-2.49.0-h57928b3_2 
  git-lfs            conda-forge/win-64::git-lfs-3.7.0-h86e1c39_0 



Downloading and Extracting Packages:

Preparing transaction: done
Verifying transaction: done
Executing transaction: done

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 9f909201-3a4a-421c-a4be-b717f4892126
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 7/27/2025, 7:29:45 AM (1753626585237)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 2 (125.5 MB) tarball(s).
Will remove 1 index cache(s).
There are no unused package(s) to remove.
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 01674db3-87e4-4ad2-9d70-2b25ebc2a81b
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge 7zip
# timestamp: 7/27/2025, 7:30:52 AM (1753626652035)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge 7zip
Channels:
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - 7zip


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    7zip-24.08                 |       h49e36cd_1         1.1 MB  conda-forge
    ------------------------------------------------------------
                                           Total:         1.1 MB

The following NEW packages will be INSTALLED:

  7zip               conda-forge/win-64::7zip-24.08-h49e36cd_1 



Downloading and Extracting Packages:

Preparing transaction: done
Verifying transaction: done
Executing transaction: done

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 7fd2d50c-e2a6-40a7-8bcc-5944ea90730b
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 7/27/2025, 7:30:56 AM (1753626656267)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 1 (1.1 MB) tarball(s).
Will remove 1 index cache(s).
There are no unused package(s) to remove.
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 5a33f0f7-cacb-420d-9bf6-e96dfa8ed9b3
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y nodejs pnpm -c conda-forge
# timestamp: 7/27/2025, 7:32:15 AM (1753626735793)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y nodejs pnpm -c conda-forge
Channels:
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - nodejs
    - pnpm


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    nodejs-24.4.1              |       he453025_0        28.6 MB  conda-forge
    pnpm-10.13.1               |       h785286a_1         3.1 MB  conda-forge
    ------------------------------------------------------------
                                           Total:        31.7 MB

The following NEW packages will be INSTALLED:

  nodejs             conda-forge/win-64::nodejs-24.4.1-he453025_0 
  pnpm               conda-forge/win-64::pnpm-10.13.1-h785286a_1 



Downloading and Extracting Packages:

Preparing transaction: done
Verifying transaction: done
Executing transaction: done

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 9e3b3403-65ee-4351-8d8d-941890c33bcc
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 7/27/2025, 7:32:19 AM (1753626739578)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 2 (31.7 MB) tarball(s).
Will remove 1 index cache(s).
There are no unused package(s) to remove.
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: d20bfa55-4601-457f-b44a-685d57e83fab
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge ffmpeg
# timestamp: 7/27/2025, 7:32:57 AM (1753626777010)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge ffmpeg
Channels:
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - ffmpeg


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    ffmpeg-4.3.1               |       ha925a31_0        26.2 MB  conda-forge
    ------------------------------------------------------------
                                           Total:        26.2 MB

The following NEW packages will be INSTALLED:

  ffmpeg             conda-forge/win-64::ffmpeg-4.3.1-ha925a31_0 



Downloading and Extracting Packages:

Preparing transaction: done
Verifying transaction: done
Executing transaction: done

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 16a6d5f8-72c6-40e7-b9cd-f3c7990525d3
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 7/27/2025, 7:33:00 AM (1753626780979)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 1 (26.2 MB) tarball(s).
Will remove 1 index cache(s).
There are no unused package(s) to remove.
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 8996d624-94be-410e-8946-61725e03febf
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y cudnn libzlib-wapi -c conda-forge
# timestamp: 7/27/2025, 7:44:32 AM (1753627472940)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y cudnn libzlib-wapi -c conda-forge
Channels:
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - cudnn
    - libzlib-wapi


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    cuda-nvrtc-12.9.86         |       he0c23c2_0        55.8 MB  conda-forge
    cuda-version-12.9          |       h4f385c5_3          21 KB  conda-forge
    cudnn-9.10.1.4             |       h32ff316_1          19 KB  conda-forge
    libcublas-12.9.1.4         |       he0c23c2_0       439.9 MB  conda-forge
    libcudnn-9.10.1.4          |       hca898b4_1       486.3 MB  conda-forge
    libcudnn-dev-9.10.1.4      |       hca898b4_1         152 KB  conda-forge
    libzlib-1.2.13             |       h2466b09_6          55 KB  conda-forge
    libzlib-wapi-1.2.13        |       h2466b09_6          55 KB  conda-forge
    zlib-1.2.13                |       h2466b09_6         105 KB  conda-forge
    ------------------------------------------------------------
                                           Total:       982.4 MB

The following NEW packages will be INSTALLED:

  cuda-nvrtc         conda-forge/win-64::cuda-nvrtc-12.9.86-he0c23c2_0 
  cuda-version       conda-forge/noarch::cuda-version-12.9-h4f385c5_3 
  cudnn              conda-forge/win-64::cudnn-9.10.1.4-h32ff316_1 
  libcublas          conda-forge/win-64::libcublas-12.9.1.4-he0c23c2_0 
  libcudnn           conda-forge/win-64::libcudnn-9.10.1.4-hca898b4_1 
  libcudnn-dev       conda-forge/win-64::libcudnn-dev-9.10.1.4-hca898b4_1 
  libzlib            conda-forge/win-64::libzlib-1.2.13-h2466b09_6 
  libzlib-wapi       conda-forge/win-64::libzlib-wapi-1.2.13-h2466b09_6 

The following packages will be UPDATED:

  zlib                    pkgs/main::zlib-1.2.13-h8cc25b3_1 --> conda-forge::zlib-1.2.13-h2466b09_6 



Downloading and Extracting Packages:

Preparing transaction: done
Verifying transaction: done
Executing transaction: done

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: ccadeb08-954a-4eea-aa5c-a8342f4013ea
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 7/27/2025, 7:44:36 AM (1753627476860)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 9 (982.4 MB) tarball(s).
Will remove 1 index cache(s).
Will remove 3 (810 KB) package(s).
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 6dd44556-4ea7-462a-8ab7-c00c64aabfb5
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y cuda -c nvidia/label/cuda-12.1.0
# timestamp: 7/27/2025, 7:56:08 AM (1753628168046)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y cuda -c nvidia/label/cuda-12.1.0
Channels:
 - nvidia/label/cuda-12.1.0
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - cuda


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    cuda-12.1.0                |                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-cccl-12.1.55          |                0         1.2 MB  nvidia/label/cuda-12.1.0
    cuda-command-line-tools-12.1.0|                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-compiler-12.1.0       |                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-cudart-12.1.55        |                0         965 KB  nvidia/label/cuda-12.1.0
    cuda-cudart-dev-12.1.55    |                0         547 KB  nvidia/label/cuda-12.1.0
    cuda-cuobjdump-12.1.55     |                0         3.7 MB  nvidia/label/cuda-12.1.0
    cuda-cupti-12.1.62         |                0        11.6 MB  nvidia/label/cuda-12.1.0
    cuda-cuxxfilt-12.1.55      |                0         163 KB  nvidia/label/cuda-12.1.0
    cuda-demo-suite-12.1.55    |                0         4.7 MB  nvidia/label/cuda-12.1.0
    cuda-documentation-12.1.55 |                0          89 KB  nvidia/label/cuda-12.1.0
    cuda-libraries-12.1.0      |                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-libraries-dev-12.1.0  |                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-nsight-compute-12.1.0 |                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-nvcc-12.1.66          |                0        55.3 MB  nvidia/label/cuda-12.1.0
    cuda-nvdisasm-12.1.55      |                0        48.0 MB  nvidia/label/cuda-12.1.0
    cuda-nvml-dev-12.1.55      |                0          88 KB  nvidia/label/cuda-12.1.0
    cuda-nvprof-12.1.55        |                0         1.6 MB  nvidia/label/cuda-12.1.0
    cuda-nvprune-12.1.55       |                0         150 KB  nvidia/label/cuda-12.1.0
    cuda-nvrtc-dev-12.1.55     |                0        16.5 MB  nvidia/label/cuda-12.1.0
    cuda-nvtx-12.1.66          |                0          41 KB  nvidia/label/cuda-12.1.0
    cuda-nvvp-12.1.55          |                0       113.6 MB  nvidia/label/cuda-12.1.0
    cuda-opencl-12.1.56        |                0          10 KB  nvidia/label/cuda-12.1.0
    cuda-opencl-dev-12.1.56    |                0          60 KB  nvidia/label/cuda-12.1.0
    cuda-profiler-api-12.1.55  |                0          18 KB  nvidia/label/cuda-12.1.0
    cuda-runtime-12.1.0        |                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-sanitizer-api-12.1.55 |                0        12.9 MB  nvidia/label/cuda-12.1.0
    cuda-toolkit-12.1.0        |                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-tools-12.1.0          |                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-visual-tools-12.1.0   |                0           1 KB  nvidia/label/cuda-12.1.0
    libcublas-dev-12.1.0.26    |                0       348.3 MB  nvidia/label/cuda-12.1.0
    libcufft-11.0.2.4          |                0           6 KB  nvidia/label/cuda-12.1.0
    libcufft-dev-11.0.2.4      |                0       102.6 MB  nvidia/label/cuda-12.1.0
    libcurand-10.3.2.56        |                0           3 KB  nvidia/label/cuda-12.1.0
    libcurand-dev-10.3.2.56    |                0        50.0 MB  nvidia/label/cuda-12.1.0
    libcusolver-11.4.4.55      |                0          30 KB  nvidia/label/cuda-12.1.0
    libcusolver-dev-11.4.4.55  |                0        95.7 MB  nvidia/label/cuda-12.1.0
    libcusparse-12.0.2.55      |                0          12 KB  nvidia/label/cuda-12.1.0
    libcusparse-dev-12.0.2.55  |                0       162.5 MB  nvidia/label/cuda-12.1.0
    libnpp-12.0.2.50           |                0         305 KB  nvidia/label/cuda-12.1.0
    libnpp-dev-12.0.2.50       |                0       135.6 MB  nvidia/label/cuda-12.1.0
    libnvjitlink-12.1.55       |                0        67.3 MB  nvidia/label/cuda-12.1.0
    libnvjitlink-dev-12.1.55   |                0        13.8 MB  nvidia/label/cuda-12.1.0
    libnvjpeg-12.1.0.39        |                0           5 KB  nvidia/label/cuda-12.1.0
    libnvjpeg-dev-12.1.0.39    |                0         2.0 MB  nvidia/label/cuda-12.1.0
    libnvvm-samples-12.1.55    |                0          32 KB  nvidia/label/cuda-12.1.0
    nsight-compute-2023.1.0.15 |                0       601.8 MB  nvidia/label/cuda-12.1.0
    ------------------------------------------------------------
                                           Total:        1.81 GB

The following NEW packages will be INSTALLED:

  cuda               nvidia/label/cuda-12.1.0/win-64::cuda-12.1.0-0
  cuda-cccl          nvidia/label/cuda-12.1.0/win-64::cuda-cccl-12.1.55-0
  cuda-command-line~ nvidia/label/cuda-12.1.0/win-64::cuda-command-line-tools-12.1.0-0
  cuda-compiler      nvidia/label/cuda-12.1.0/win-64::cuda-compiler-12.1.0-0
  cuda-cudart        nvidia/label/cuda-12.1.0/win-64::cuda-cudart-12.1.55-0
  cuda-cudart-dev    nvidia/label/cuda-12.1.0/win-64::cuda-cudart-dev-12.1.55-0
  cuda-cuobjdump     nvidia/label/cuda-12.1.0/win-64::cuda-cuobjdump-12.1.55-0
  cuda-cupti         nvidia/label/cuda-12.1.0/win-64::cuda-cupti-12.1.62-0
  cuda-cuxxfilt      nvidia/label/cuda-12.1.0/win-64::cuda-cuxxfilt-12.1.55-0
  cuda-demo-suite    nvidia/label/cuda-12.1.0/win-64::cuda-demo-suite-12.1.55-0
  cuda-documentation nvidia/label/cuda-12.1.0/win-64::cuda-documentation-12.1.55-0
  cuda-libraries     nvidia/label/cuda-12.1.0/win-64::cuda-libraries-12.1.0-0
  cuda-libraries-dev nvidia/label/cuda-12.1.0/win-64::cuda-libraries-dev-12.1.0-0
  cuda-nsight-compu~ nvidia/label/cuda-12.1.0/win-64::cuda-nsight-compute-12.1.0-0
  cuda-nvcc          nvidia/label/cuda-12.1.0/win-64::cuda-nvcc-12.1.66-0
  cuda-nvdisasm      nvidia/label/cuda-12.1.0/win-64::cuda-nvdisasm-12.1.55-0
  cuda-nvml-dev      nvidia/label/cuda-12.1.0/win-64::cuda-nvml-dev-12.1.55-0
  cuda-nvprof        nvidia/label/cuda-12.1.0/win-64::cuda-nvprof-12.1.55-0
  cuda-nvprune       nvidia/label/cuda-12.1.0/win-64::cuda-nvprune-12.1.55-0
  cuda-nvrtc-dev     nvidia/label/cuda-12.1.0/win-64::cuda-nvrtc-dev-12.1.55-0
  cuda-nvtx          nvidia/label/cuda-12.1.0/win-64::cuda-nvtx-12.1.66-0
  cuda-nvvp          nvidia/label/cuda-12.1.0/win-64::cuda-nvvp-12.1.55-0
  cuda-opencl        nvidia/label/cuda-12.1.0/win-64::cuda-opencl-12.1.56-0
  cuda-opencl-dev    nvidia/label/cuda-12.1.0/win-64::cuda-opencl-dev-12.1.56-0
  cuda-profiler-api  nvidia/label/cuda-12.1.0/win-64::cuda-profiler-api-12.1.55-0
  cuda-runtime       nvidia/label/cuda-12.1.0/win-64::cuda-runtime-12.1.0-0
  cuda-sanitizer-api nvidia/label/cuda-12.1.0/win-64::cuda-sanitizer-api-12.1.55-0
  cuda-toolkit       nvidia/label/cuda-12.1.0/win-64::cuda-toolkit-12.1.0-0
  cuda-tools         nvidia/label/cuda-12.1.0/win-64::cuda-tools-12.1.0-0
  cuda-visual-tools  nvidia/label/cuda-12.1.0/win-64::cuda-visual-tools-12.1.0-0
  libcublas-dev      nvidia/label/cuda-12.1.0/win-64::libcublas-dev-12.1.0.26-0
  libcufft           nvidia/label/cuda-12.1.0/win-64::libcufft-11.0.2.4-0
  libcufft-dev       nvidia/label/cuda-12.1.0/win-64::libcufft-dev-11.0.2.4-0
  libcurand          nvidia/label/cuda-12.1.0/win-64::libcurand-10.3.2.56-0
  libcurand-dev      nvidia/label/cuda-12.1.0/win-64::libcurand-dev-10.3.2.56-0
  libcusolver        nvidia/label/cuda-12.1.0/win-64::libcusolver-11.4.4.55-0
  libcusolver-dev    nvidia/label/cuda-12.1.0/win-64::libcusolver-dev-11.4.4.55-0
  libcusparse        nvidia/label/cuda-12.1.0/win-64::libcusparse-12.0.2.55-0
  libcusparse-dev    nvidia/label/cuda-12.1.0/win-64::libcusparse-dev-12.0.2.55-0
  libnpp             nvidia/label/cuda-12.1.0/win-64::libnpp-12.0.2.50-0
  libnpp-dev         nvidia/label/cuda-12.1.0/win-64::libnpp-dev-12.0.2.50-0
  libnvjitlink       nvidia/label/cuda-12.1.0/win-64::libnvjitlink-12.1.55-0
  libnvjitlink-dev   nvidia/label/cuda-12.1.0/win-64::libnvjitlink-dev-12.1.55-0
  libnvjpeg          nvidia/label/cuda-12.1.0/win-64::libnvjpeg-12.1.0.39-0
  libnvjpeg-dev      nvidia/label/cuda-12.1.0/win-64::libnvjpeg-dev-12.1.0.39-0
  libnvvm-samples    nvidia/label/cuda-12.1.0/win-64::libnvvm-samples-12.1.55-0
  nsight-compute     nvidia/label/cuda-12.1.0/win-64::nsight-compute-2023.1.0.15-0



Downloading and Extracting Packages:
 
Preparing transaction: done 
Verifying transaction: done 
Executing transaction: done 
 
(base) C:\pinokio\bin>
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 

######################################################################
#
# group: undefined
# id: e70c1419-6f11-49f4-b784-1af6e5f2336b
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && git clone https://github.com/pinokiocomputer/python py
# timestamp: 7/27/2025, 7:56:12 AM (1753628172197)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && git clone https://github.com/pinokiocomputer/python py
Cloning into 'py'...
remote: Enumerating objects: 54, done.
remote: Counting objects: 100% (54/54), done.
remote: Compressing objects: 100% (38/38), done.
remote: Total 54 (delta 16), reused 53 (delta 15), pack-reused 0 (from 0)
Receiving objects: 100% (54/54), 14.65 KiB | 441.00 KiB/s, done.
Resolving deltas: 100% (16/16), done.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 9ea0b6dd-0779-40a3-a127-d5bc4eb2c5c5
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && python -m venv C:\pinokio\bin\py\env && C:\pinokio\bin\py\env\Scripts\activate C:\pinokio\bin\py\env && C:\pinokio\bin\py\env\Scripts\deactivate && C:\pinokio\bin\py\env\Scripts\activate C:\pinokio\bin\py\env && pip install -r requirements.txt
# timestamp: 7/27/2025, 7:56:36 AM (1753628196507)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin\py>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && python -m venv C:\pinokio\bin\py\env && C:\pinokio\bin\py\env\Scripts\activate C:\pinokio\bin\py\env && C:\pinokio\bin\py\env\Scripts\deactivate && C:\pinokio\bin\py\env\Scripts\activate C:\pinokio\bin\py\env && pip install -r requirements.txt
Collecting importlib_metadata
  Downloading importlib_metadata-8.7.0-py3-none-any.whl (27 kB)
Collecting fastapi
  Downloading fastapi-0.116.1-py3-none-any.whl (95 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 95.6/95.6 kB 2.8 MB/s eta 0:00:00
Collecting uvicorn[standard]
  Downloading uvicorn-0.35.0-py3-none-any.whl (66 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 66.4/66.4 kB 3.5 MB/s eta 0:00:00
Collecting zipp>=3.20
  Downloading zipp-3.23.0-py3-none-any.whl (10 kB)
Collecting pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4
  Downloading pydantic-2.11.7-py3-none-any.whl (444 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 444.8/444.8 kB 151.9 kB/s eta 0:00:00
Collecting typing-extensions>=4.8.0
  Downloading typing_extensions-4.14.1-py3-none-any.whl (43 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 43.9/43.9 kB 362.0 kB/s eta 0:00:00
Collecting starlette<0.48.0,>=0.40.0
  Downloading starlette-0.47.2-py3-none-any.whl (72 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 73.0/73.0 kB 2.0 MB/s eta 0:00:00
Collecting click>=7.0
  Downloading click-8.2.1-py3-none-any.whl (102 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 102.2/102.2 kB 5.7 MB/s eta 0:00:00
Collecting h11>=0.8
  Downloading h11-0.16.0-py3-none-any.whl (37 kB)
Collecting python-dotenv>=0.13
  Downloading python_dotenv-1.1.1-py3-none-any.whl (20 kB)
Collecting httptools>=0.6.3
  Downloading httptools-0.6.4-cp310-cp310-win_amd64.whl (88 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 88.3/88.3 kB ? eta 0:00:00
Collecting websockets>=10.4
  Downloading websockets-15.0.1-cp310-cp310-win_amd64.whl (176 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 176.8/176.8 kB 5.4 MB/s eta 0:00:00
Collecting pyyaml>=5.1
  Downloading PyYAML-6.0.2-cp310-cp310-win_amd64.whl (161 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 161.8/161.8 kB 10.1 MB/s eta 0:00:00
Collecting watchfiles>=0.13
  Downloading watchfiles-1.1.0-cp310-cp310-win_amd64.whl (292 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 292.2/292.2 kB 6.0 MB/s eta 0:00:00
Collecting colorama>=0.4
  Downloading colorama-0.4.6-py2.py3-none-any.whl (25 kB)
Collecting annotated-types>=0.6.0
  Downloading annotated_types-0.7.0-py3-none-any.whl (13 kB)
Collecting typing-inspection>=0.4.0
  Downloading typing_inspection-0.4.1-py3-none-any.whl (14 kB)
Collecting pydantic-core==2.33.2
  Downloading pydantic_core-2.33.2-cp310-cp310-win_amd64.whl (2.0 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 2.0/2.0 MB 2.5 MB/s eta 0:00:00
Collecting anyio<5,>=3.6.2
  Downloading anyio-4.9.0-py3-none-any.whl (100 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 100.9/100.9 kB 2.8 MB/s eta 0:00:00
Collecting exceptiongroup>=1.0.2
  Downloading exceptiongroup-1.3.0-py3-none-any.whl (16 kB)
Collecting idna>=2.8
  Downloading idna-3.10-py3-none-any.whl (70 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 70.4/70.4 kB 955.6 kB/s eta 0:00:00
Collecting sniffio>=1.1
  Downloading sniffio-1.3.1-py3-none-any.whl (10 kB)
Installing collected packages: zipp, websockets, typing-extensions, sniffio, pyyaml, python-dotenv, idna, httptools, h11, colorama, annotated-types, typing-inspection, pydantic-core, importlib_metadata, exceptiongroup, click, uvicorn, pydantic, anyio, watchfiles, starlette, fastapi
Successfully installed annotated-types-0.7.0 anyio-4.9.0 click-8.2.1 colorama-0.4.6 exceptiongroup-1.3.0 fastapi-0.116.1 h11-0.16.0 httptools-0.6.4 idna-3.10 importlib_metadata-8.7.0 pydantic-2.11.7 pydantic-core-2.33.2 python-dotenv-1.1.1 pyyaml-6.0.2 sniffio-1.3.1 starlette-0.47.2 typing-extensions-4.14.1 typing-inspection-0.4.1 uvicorn-0.35.0 watchfiles-1.1.0 websockets-15.0.1 zipp-3.23.0      

[notice] A new release of pip is available: 23.0.1 -> 25.1.1
[notice] To update, run: python.exe -m pip install --upgrade pip

(env) (base) C:\pinokio\bin\py>

######################################################################
#
# group: undefined
# id: 62e87144-273b-4f11-a1ee-e68ef1cc0d37
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && npm init -y playwright@latest -- --quiet
# timestamp: 7/27/2025, 8:03:02 AM (1753628582392)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin\playwright>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && npm init -y playwright@latest -- --quiet

> npx
> create-playwright --quiet

Getting started with writing end-to-end tests with Playwright:
Initializing project in '.'
Initializing NPM project (npm init -y)…
Wrote to C:\pinokio\bin\playwright\package.json:

{
  "name": "playwright",
  "version": "1.0.0",
  "description": "",
  "main": "index.js",
  "scripts": {
    "test": "echo \"Error: no test specified\" && exit 1"
  },
  "keywords": [],
  "author": "",
  "license": "ISC",
  "type": "commonjs"
}



Installing Playwright Test (npm install --save-dev @playwright/test)…

added 4 packages, and audited 5 packages in 6s

found 0 vulnerabilities
Installing Types (npm install --save-dev @types/node)…

added 2 packages, and audited 7 packages in 1s

found 0 vulnerabilities
Writing playwright.config.ts.
Writing tests\example.spec.ts.
Writing tests-examples\demo-todo-app.spec.ts.
Writing package.json.
Downloading browsers (npx playwright install)…
Downloading Chromium 139.0.7258.5 (playwright build v1181) from https://cdn.playwright.dev/dbazure/download/playwright/builds/chromium/1181/chromium-win64.zip
146.9 MiB [====================] 100% 0.0s
Chromium 139.0.7258.5 (playwright build v1181) downloaded to C:\pinokio\bin\playwright\browsers\chromium-1181
Downloading Chromium Headless Shell 139.0.7258.5 (playwright build v1181) from https://cdn.playwright.dev/dbazure/download/playwright/builds/chromium/1181/chromium-headless-shell-win64.zip
90.4 MiB [====================] 100% 0.0s
Chromium Headless Shell 139.0.7258.5 (playwright build v1181) downloaded to C:\pinokio\bin\playwright\browsers\chromium_headless_shell-1181
Downloading Firefox 140.0.2 (playwright build v1489) from https://cdn.playwright.dev/dbazure/download/playwright/builds/firefox/1489/firefox-win64.zip
93.5 MiB [====================] 100% 0.0s
Firefox 140.0.2 (playwright build v1489) downloaded to C:\pinokio\bin\playwright\browsers\firefox-1489
Downloading Webkit 26.0 (playwright build v2191) from https://cdn.playwright.dev/dbazure/download/playwright/builds/webkit/2191/webkit-win64.zip
56.9 MiB [====================] 100% 0.0s
Webkit 26.0 (playwright build v2191) downloaded to C:\pinokio\bin\playwright\browsers\webkit-2191
Downloading FFMPEG playwright build v1011 from https://cdn.playwright.dev/dbazure/download/playwright/builds/ffmpeg/1011/ffmpeg-win64.zip
1.3 MiB [====================] 100% 0.0s
FFMPEG playwright build v1011 downloaded to C:\pinokio\bin\playwright\browsers\ffmpeg-1011
Downloading Winldd playwright build v1007 from https://cdn.playwright.dev/dbazure/download/playwright/builds/winldd/1007/winldd-win64.zip
0.1 MiB [====================] 100% 0.0s
Winldd playwright build v1007 downloaded to C:\pinokio\bin\playwright\browsers\winldd-1007
✔ Success! Created a Playwright Test project at C:\pinokio\bin\playwright

Inside that directory, you can run several commands:

  npx playwright test
    Runs the end-to-end tests.

  npx playwright test --ui
    Starts the interactive UI mode.

  npx playwright test --project=chromium
    Runs the tests only on Desktop Chrome.

  npx playwright test example
    Runs the tests in a specific file.

  npx playwright test --debug
    Runs the tests in debug mode.

  npx playwright codegen
    Auto generate tests with Codegen.

We suggest that you begin by typing:

    npx playwright test

And check out the following files:
  - .\tests\example.spec.ts - Example end-to-end test
  - .\tests-examples\demo-todo-app.spec.ts - Demo Todo App end-to-end tests
  - .\playwright.config.ts - Playwright Test configuration

Visit https://playwright.dev/docs/intro for more information. ✨

Happy hacking! 🎭

(base) C:\pinokio\bin\playwright>

######################################################################
#
# group: undefined
# id: 37af9318-2974-4665-90df-3d70fc4e765b
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 7/27/2025, 8:03:06 AM (1753628586776)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 47 (1.81 GB) tarball(s).
Will remove 1 index cache(s).
Will remove 10 (40 KB) package(s).
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: c25006c7-0fdf-4c95-979f-e249bef7fb15
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge huggingface_hub
# timestamp: 7/27/2025, 8:03:56 AM (1753628636456)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge huggingface_hub
Channels:
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - huggingface_hub


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    _python_abi3_support-1.0   |       hd8ed1ab_2           8 KB  conda-forge
    cpython-3.10.18            |  py310hd8ed1ab_0          49 KB  conda-forge
    filelock-3.18.0            |     pyhd8ed1ab_0          17 KB  conda-forge
    fsspec-2025.7.0            |     pyhd8ed1ab_0         142 KB  conda-forge
    hf-xet-1.1.5               |   py39h17685eb_3         2.3 MB  conda-forge
    huggingface_hub-0.33.4     |     pyhd8ed1ab_0         311 KB  conda-forge
    python-gil-3.10.18         |       hd8ed1ab_0          49 KB  conda-forge
    pyyaml-6.0.2               |  py310h38315fa_2         154 KB  conda-forge
    yaml-0.2.5                 |       h6a83c73_3          62 KB  conda-forge
    ------------------------------------------------------------
                                           Total:         3.1 MB

The following NEW packages will be INSTALLED:

  _python_abi3_supp~ conda-forge/noarch::_python_abi3_support-1.0-hd8ed1ab_2 
  cpython            conda-forge/noarch::cpython-3.10.18-py310hd8ed1ab_0 
  filelock           conda-forge/noarch::filelock-3.18.0-pyhd8ed1ab_0 
  fsspec             conda-forge/noarch::fsspec-2025.7.0-pyhd8ed1ab_0 
  hf-xet             conda-forge/win-64::hf-xet-1.1.5-py39h17685eb_3 
  huggingface_hub    conda-forge/noarch::huggingface_hub-0.33.4-pyhd8ed1ab_0 
  python-gil         conda-forge/noarch::python-gil-3.10.18-hd8ed1ab_0 
  pyyaml             conda-forge/win-64::pyyaml-6.0.2-py310h38315fa_2 
  yaml               conda-forge/win-64::yaml-0.2.5-h6a83c73_3 



Downloading and Extracting Packages:

Preparing transaction: done
Verifying transaction: done
Executing transaction: done

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 63a5eef0-b2ef-4a2c-9778-32e96f1ba30a
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 7/27/2025, 8:04:00 AM (1753628640439)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 9 (3.1 MB) tarball(s).
Will remove 1 index cache(s).
Will remove 3 (453 KB) package(s).
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 71937011-fe36-429b-9471-e80e038d37ad
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge uv
# timestamp: 7/27/2025, 8:05:15 AM (1753628715963)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge uv
Channels:
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - uv


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    uv-0.8.3                   |       h579f82e_0        15.0 MB  conda-forge
    ------------------------------------------------------------
                                           Total:        15.0 MB

The following NEW packages will be INSTALLED:

  uv                 conda-forge/win-64::uv-0.8.3-h579f82e_0 



Downloading and Extracting Packages:

Preparing transaction: done
Verifying transaction: done
Executing transaction: done

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: b9c9d2c2-9340-4ae6-a4bd-0c9fc7a3cae9
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/27/2025, 8:05:19 AM (1753628719586)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# packages in environment at C:\pinokio\bin\miniconda:
#
# Name                        Version          Build              Channel
7zip                          24.08            h49e36cd_1         conda-forge
_python_abi3_support          1.0              hd8ed1ab_2         conda-forge
anaconda-anon-usage           0.5.0            py310hfc23b7f_100
anaconda_powershell_prompt    1.1.0            haa95532_0
anaconda_prompt               1.1.0            haa95532_0
annotated-types               0.6.0            py310haa95532_0
archspec                      0.2.3            pyhd3eb1b0_0
boltons                       24.1.0           py310haa95532_0
brotli-python                 1.0.9            py310h5da7b33_9
bzip2                         1.0.8            h2bbff1b_6
ca-certificates               2025.7.14        h4c7d964_0         conda-forge
certifi                       2025.7.14        pyhd8ed1ab_0       conda-forge
cffi                          1.17.1           py310h827c3e9_1
charset-normalizer            3.3.2            pyhd3eb1b0_0
colorama                      0.4.6            py310haa95532_0
conda                         25.5.1           py310h5588dad_0    conda-forge
conda-anaconda-telemetry      0.1.2            py310haa95532_0
conda-anaconda-tos            0.1.2            py310haa95532_0
conda-content-trust           0.2.0            py310haa95532_1
conda-libmamba-solver         25.1.1           pyhd3eb1b0_0
conda-package-handling        2.4.0            py310haa95532_0
conda-package-streaming       0.11.0           py310haa95532_0
cpp-expected                  1.1.0            h214f63a_0
cpython                       3.10.18          py310hd8ed1ab_0    conda-forge
cryptography                  43.0.3           py310hbd6ee87_1
cuda                          12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-cccl                     12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-command-line-tools       12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-compiler                 12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-cudart                   12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-cudart-dev               12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-cuobjdump                12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-cupti                    12.1.62          0                  nvidia/label/cuda-12.1.0
cuda-cuxxfilt                 12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-demo-suite               12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-documentation            12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-libraries                12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-libraries-dev            12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-nsight-compute           12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-nvcc                     12.1.66          0                  nvidia/label/cuda-12.1.0
cuda-nvdisasm                 12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-nvml-dev                 12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-nvprof                   12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-nvprune                  12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-nvrtc                    12.9.86          he0c23c2_0         conda-forge
cuda-nvrtc-dev                12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-nvtx                     12.1.66          0                  nvidia/label/cuda-12.1.0
cuda-nvvp                     12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-opencl                   12.1.56          0                  nvidia/label/cuda-12.1.0
cuda-opencl-dev               12.1.56          0                  nvidia/label/cuda-12.1.0
cuda-profiler-api             12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-runtime                  12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-sanitizer-api            12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-toolkit                  12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-tools                    12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-version                  12.9             h4f385c5_3         conda-forge
cuda-visual-tools             12.1.0           0                  nvidia/label/cuda-12.1.0
cudnn                         9.10.1.4         h32ff316_1         conda-forge
distro                        1.9.0            py310haa95532_0
ffmpeg                        4.3.1            ha925a31_0         conda-forge
filelock                      3.18.0           pyhd8ed1ab_0       conda-forge
fmt                           9.1.0            h6d14046_1
frozendict                    2.4.2            py310h2bbff1b_0
fsspec                        2025.7.0         pyhd8ed1ab_0       conda-forge
git                           2.49.0           h57928b3_2         conda-forge
git-lfs                       3.7.0            h86e1c39_0         conda-forge
hf-xet                        1.1.5            py39h17685eb_3     conda-forge
huggingface_hub               0.33.4           pyhd8ed1ab_0       conda-forge
idna                          3.7              py310haa95532_0
jsonpatch                     1.33             py310haa95532_1
jsonpointer                   2.1              pyhd3eb1b0_0
libarchive                    3.7.7            h9243413_0
libcublas                     12.9.1.4         he0c23c2_0         conda-forge
libcublas-dev                 12.1.0.26        0                  nvidia/label/cuda-12.1.0
libcudnn                      9.10.1.4         hca898b4_1         conda-forge
libcudnn-dev                  9.10.1.4         hca898b4_1         conda-forge
libcufft                      11.0.2.4         0                  nvidia/label/cuda-12.1.0
libcufft-dev                  11.0.2.4         0                  nvidia/label/cuda-12.1.0
libcurand                     10.3.2.56        0                  nvidia/label/cuda-12.1.0
libcurand-dev                 10.3.2.56        0                  nvidia/label/cuda-12.1.0
libcurl                       8.11.1           haff574d_0
libcusolver                   11.4.4.55        0                  nvidia/label/cuda-12.1.0
libcusolver-dev               11.4.4.55        0                  nvidia/label/cuda-12.1.0
libcusparse                   12.0.2.55        0                  nvidia/label/cuda-12.1.0
libcusparse-dev               12.0.2.55        0                  nvidia/label/cuda-12.1.0
libffi                        3.4.4            hd77b12b_1
libiconv                      1.16             h2bbff1b_3
libmamba                      2.0.5            hcd6fe79_1
libmambapy                    2.0.5            py310h214f63a_1
libnpp                        12.0.2.50        0                  nvidia/label/cuda-12.1.0
libnpp-dev                    12.0.2.50        0                  nvidia/label/cuda-12.1.0
libnvjitlink                  12.1.55          0                  nvidia/label/cuda-12.1.0
libnvjitlink-dev              12.1.55          0                  nvidia/label/cuda-12.1.0
libnvjpeg                     12.1.0.39        0                  nvidia/label/cuda-12.1.0
libnvjpeg-dev                 12.1.0.39        0                  nvidia/label/cuda-12.1.0
libnvvm-samples               12.1.55          0                  nvidia/label/cuda-12.1.0
libsolv                       0.7.30           hf2fb9eb_1
libsqlite                     3.47.2           h67fdade_0         conda-forge
libssh2                       1.11.1           h2addb87_0
libxml2                       2.13.5           h24da03e_0
libzlib                       1.2.13           h2466b09_6         conda-forge
libzlib-wapi                  1.2.13           h2466b09_6         conda-forge
lz4-c                         1.9.4            h2bbff1b_1
markdown-it-py                2.2.0            py310haa95532_1
mdurl                         0.1.0            py310haa95532_0
menuinst                      2.2.0            py310h5da7b33_1
nlohmann_json                 3.11.2           h6c2663c_0
nodejs                        24.4.1           he453025_0         conda-forge
nsight-compute                2023.1.0.15      0                  nvidia/label/cuda-12.1.0
openssl                       3.5.1            h725018a_0         conda-forge
packaging                     24.2             py310haa95532_0
pcre2                         10.42            h0ff8eda_1
pip                           25.0             py310haa95532_0
platformdirs                  3.10.0           py310haa95532_0
pluggy                        1.5.0            py310haa95532_0
pnpm                          10.13.1          h785286a_1         conda-forge
pybind11-abi                  5                hd3eb1b0_0
pycosat                       0.6.6            py310h827c3e9_2
pycparser                     2.21             pyhd3eb1b0_0
pydantic                      2.10.3           py310haa95532_0
pydantic-core                 2.27.1           py310h636fa0f_0
pygments                      2.15.1           py310haa95532_1
pysocks                       1.7.1            py310haa95532_0
python                        3.10.16          h4607a30_1
python-gil                    3.10.18          hd8ed1ab_0         conda-forge
python_abi                    3.10             2_cp310            conda-forge
pyyaml                        6.0.2            py310h38315fa_2    conda-forge
reproc                        14.2.4           hd77b12b_2
reproc-cpp                    14.2.4           hd77b12b_2
requests                      2.32.3           py310haa95532_1
rich                          13.9.4           py310haa95532_0
ruamel.yaml                   0.18.6           py310h827c3e9_0
ruamel.yaml.clib              0.2.8            py310h827c3e9_0
setuptools                    75.8.0           py310haa95532_0
simdjson                      3.10.1           h214f63a_0
spdlog                        1.11.0           h59b6b97_0
sqlite                        3.47.2           h2466b09_0         conda-forge
tk                            8.6.14           h0416ee5_0
tqdm                          4.67.1           py310h9909e9c_0
truststore                    0.10.0           py310haa95532_0
typing-extensions             4.12.2           py310haa95532_0
typing_extensions             4.12.2           py310haa95532_0
tzdata                        2025a            h04d1e81_0
ucrt                          10.0.22621.0     h57928b3_1         conda-forge
urllib3                       2.3.0            py310haa95532_0
uv                            0.8.3            h579f82e_0         conda-forge
vc                            14.42            haa95532_4
vc14_runtime                  14.44.35208      h818238b_30        conda-forge
vs2015_runtime                14.44.35208      h38c0c73_30        conda-forge
wheel                         0.45.1           py310haa95532_0
win_inet_pton                 1.1.0            py310haa95532_0
xz                            5.4.6            h8cc25b3_1
yaml                          0.2.5            h6a83c73_3         conda-forge
yaml-cpp                      0.8.0            hd77b12b_1
zlib                          1.2.13           h2466b09_6         conda-forge
zstandard                     0.23.0           py310h4fc1ca9_1
zstd                          1.5.6            h8880b57_0

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 770e9b29-8772-4cbd-bd74-13657a5b6162
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/27/2025, 8:05:23 AM (1753628723840)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# packages in environment at C:\pinokio\bin\miniconda:
#
# Name                        Version          Build              Channel
7zip                          24.08            h49e36cd_1         conda-forge
_python_abi3_support          1.0              hd8ed1ab_2         conda-forge
anaconda-anon-usage           0.5.0            py310hfc23b7f_100
anaconda_powershell_prompt    1.1.0            haa95532_0
anaconda_prompt               1.1.0            haa95532_0
annotated-types               0.6.0            py310haa95532_0
archspec                      0.2.3            pyhd3eb1b0_0
boltons                       24.1.0           py310haa95532_0
brotli-python                 1.0.9            py310h5da7b33_9
bzip2                         1.0.8            h2bbff1b_6
ca-certificates               2025.7.14        h4c7d964_0         conda-forge
certifi                       2025.7.14        pyhd8ed1ab_0       conda-forge
cffi                          1.17.1           py310h827c3e9_1
charset-normalizer            3.3.2            pyhd3eb1b0_0
colorama                      0.4.6            py310haa95532_0
conda                         25.5.1           py310h5588dad_0    conda-forge
conda-anaconda-telemetry      0.1.2            py310haa95532_0
conda-anaconda-tos            0.1.2            py310haa95532_0
conda-content-trust           0.2.0            py310haa95532_1
conda-libmamba-solver         25.1.1           pyhd3eb1b0_0
conda-package-handling        2.4.0            py310haa95532_0
conda-package-streaming       0.11.0           py310haa95532_0
cpp-expected                  1.1.0            h214f63a_0
cpython                       3.10.18          py310hd8ed1ab_0    conda-forge
cryptography                  43.0.3           py310hbd6ee87_1
cuda                          12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-cccl                     12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-command-line-tools       12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-compiler                 12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-cudart                   12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-cudart-dev               12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-cuobjdump                12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-cupti                    12.1.62          0                  nvidia/label/cuda-12.1.0
cuda-cuxxfilt                 12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-demo-suite               12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-documentation            12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-libraries                12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-libraries-dev            12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-nsight-compute           12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-nvcc                     12.1.66          0                  nvidia/label/cuda-12.1.0
cuda-nvdisasm                 12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-nvml-dev                 12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-nvprof                   12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-nvprune                  12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-nvrtc                    12.9.86          he0c23c2_0         conda-forge
cuda-nvrtc-dev                12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-nvtx                     12.1.66          0                  nvidia/label/cuda-12.1.0
cuda-nvvp                     12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-opencl                   12.1.56          0                  nvidia/label/cuda-12.1.0
cuda-opencl-dev               12.1.56          0                  nvidia/label/cuda-12.1.0
cuda-profiler-api             12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-runtime                  12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-sanitizer-api            12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-toolkit                  12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-tools                    12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-version                  12.9             h4f385c5_3         conda-forge
cuda-visual-tools             12.1.0           0                  nvidia/label/cuda-12.1.0
cudnn                         9.10.1.4         h32ff316_1         conda-forge
distro                        1.9.0            py310haa95532_0
ffmpeg                        4.3.1            ha925a31_0         conda-forge
filelock                      3.18.0           pyhd8ed1ab_0       conda-forge
fmt                           9.1.0            h6d14046_1
frozendict                    2.4.2            py310h2bbff1b_0
fsspec                        2025.7.0         pyhd8ed1ab_0       conda-forge
git                           2.49.0           h57928b3_2         conda-forge
git-lfs                       3.7.0            h86e1c39_0         conda-forge
hf-xet                        1.1.5            py39h17685eb_3     conda-forge
huggingface_hub               0.33.4           pyhd8ed1ab_0       conda-forge
idna                          3.7              py310haa95532_0
jsonpatch                     1.33             py310haa95532_1
jsonpointer                   2.1              pyhd3eb1b0_0
libarchive                    3.7.7            h9243413_0
libcublas                     12.9.1.4         he0c23c2_0         conda-forge
libcublas-dev                 12.1.0.26        0                  nvidia/label/cuda-12.1.0
libcudnn                      9.10.1.4         hca898b4_1         conda-forge
libcudnn-dev                  9.10.1.4         hca898b4_1         conda-forge
libcufft                      11.0.2.4         0                  nvidia/label/cuda-12.1.0
libcufft-dev                  11.0.2.4         0                  nvidia/label/cuda-12.1.0
libcurand                     10.3.2.56        0                  nvidia/label/cuda-12.1.0
libcurand-dev                 10.3.2.56        0                  nvidia/label/cuda-12.1.0
libcurl                       8.11.1           haff574d_0
libcusolver                   11.4.4.55        0                  nvidia/label/cuda-12.1.0
libcusolver-dev               11.4.4.55        0                  nvidia/label/cuda-12.1.0
libcusparse                   12.0.2.55        0                  nvidia/label/cuda-12.1.0
libcusparse-dev               12.0.2.55        0                  nvidia/label/cuda-12.1.0
libffi                        3.4.4            hd77b12b_1
libiconv                      1.16             h2bbff1b_3
libmamba                      2.0.5            hcd6fe79_1
libmambapy                    2.0.5            py310h214f63a_1
libnpp                        12.0.2.50        0                  nvidia/label/cuda-12.1.0
libnpp-dev                    12.0.2.50        0                  nvidia/label/cuda-12.1.0
libnvjitlink                  12.1.55          0                  nvidia/label/cuda-12.1.0
libnvjitlink-dev              12.1.55          0                  nvidia/label/cuda-12.1.0
libnvjpeg                     12.1.0.39        0                  nvidia/label/cuda-12.1.0
libnvjpeg-dev                 12.1.0.39        0                  nvidia/label/cuda-12.1.0
libnvvm-samples               12.1.55          0                  nvidia/label/cuda-12.1.0
libsolv                       0.7.30           hf2fb9eb_1
libsqlite                     3.47.2           h67fdade_0         conda-forge
libssh2                       1.11.1           h2addb87_0
libxml2                       2.13.5           h24da03e_0
libzlib                       1.2.13           h2466b09_6         conda-forge
libzlib-wapi                  1.2.13           h2466b09_6         conda-forge
lz4-c                         1.9.4            h2bbff1b_1
markdown-it-py                2.2.0            py310haa95532_1
mdurl                         0.1.0            py310haa95532_0
menuinst                      2.2.0            py310h5da7b33_1
nlohmann_json                 3.11.2           h6c2663c_0
nodejs                        24.4.1           he453025_0         conda-forge
nsight-compute                2023.1.0.15      0                  nvidia/label/cuda-12.1.0
openssl                       3.5.1            h725018a_0         conda-forge
packaging                     24.2             py310haa95532_0
pcre2                         10.42            h0ff8eda_1
pip                           25.0             py310haa95532_0
platformdirs                  3.10.0           py310haa95532_0
pluggy                        1.5.0            py310haa95532_0
pnpm                          10.13.1          h785286a_1         conda-forge
pybind11-abi                  5                hd3eb1b0_0
pycosat                       0.6.6            py310h827c3e9_2
pycparser                     2.21             pyhd3eb1b0_0
pydantic                      2.10.3           py310haa95532_0
pydantic-core                 2.27.1           py310h636fa0f_0
pygments                      2.15.1           py310haa95532_1
pysocks                       1.7.1            py310haa95532_0
python                        3.10.16          h4607a30_1
python-gil                    3.10.18          hd8ed1ab_0         conda-forge
python_abi                    3.10             2_cp310            conda-forge
pyyaml                        6.0.2            py310h38315fa_2    conda-forge
reproc                        14.2.4           hd77b12b_2
reproc-cpp                    14.2.4           hd77b12b_2
requests                      2.32.3           py310haa95532_1
rich                          13.9.4           py310haa95532_0
ruamel.yaml                   0.18.6           py310h827c3e9_0
ruamel.yaml.clib              0.2.8            py310h827c3e9_0
setuptools                    75.8.0           py310haa95532_0
simdjson                      3.10.1           h214f63a_0
spdlog                        1.11.0           h59b6b97_0
sqlite                        3.47.2           h2466b09_0         conda-forge
tk                            8.6.14           h0416ee5_0
tqdm                          4.67.1           py310h9909e9c_0
truststore                    0.10.0           py310haa95532_0
typing-extensions             4.12.2           py310haa95532_0
typing_extensions             4.12.2           py310haa95532_0
tzdata                        2025a            h04d1e81_0
ucrt                          10.0.22621.0     h57928b3_1         conda-forge
urllib3                       2.3.0            py310haa95532_0
uv                            0.8.3            h579f82e_0         conda-forge
vc                            14.42            haa95532_4
vc14_runtime                  14.44.35208      h818238b_30        conda-forge
vs2015_runtime                14.44.35208      h38c0c73_30        conda-forge
wheel                         0.45.1           py310haa95532_0
win_inet_pton                 1.1.0            py310haa95532_0
xz                            5.4.6            h8cc25b3_1
yaml                          0.2.5            h6a83c73_3         conda-forge
yaml-cpp                      0.8.0            hd77b12b_1
zlib                          1.2.13           h2466b09_6         conda-forge
zstandard                     0.23.0           py310h4fc1ca9_1
zstd                          1.5.6            h8880b57_0

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 69456451-df0e-49cc-be3d-690c08c25304
# index: 0
# cmd: reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled
# timestamp: 7/27/2025, 8:05:25 AM (1753628725034)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled

HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\FileSystem
    LongPathsEnabled    REG_DWORD    0x1


C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 5946c9a7-ffc3-472c-88e7-e29682305c33
# index: 0
# cmd: dir
# timestamp: 7/27/2025, 8:05:29 AM (1753628729572)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  904,328,646,656 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 5fa3881c-20f3-44ef-b5f6-72060d99487f
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && git clone https://github.com/facefusion/facefusion-pinokio.git facefusion-pinokio.git
# timestamp: 7/27/2025, 8:05:33 AM (1753628733462)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\api>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && git clone https://github.com/facefusion/facefusion-pinokio.git facefusion-pinokio.git
Cloning into 'facefusion-pinokio.git'...
remote: Enumerating objects: 453, done.
remote: Counting objects: 100% (213/213), done.
remote: Compressing objects: 100% (78/78), done.
remote: Total 453 (delta 153), reused 144 (delta 135), pack-reused 240 (from 2)
Receiving objects: 100% (453/453), 324.69 KiB | 2.18 MiB/s, done.
Resolving deltas: 100% (283/283), done.

(base) C:\pinokio\api>

######################################################################
#
# group: undefined
# id: 2d7787cb-726f-44ff-961b-ceaf430b4779
# index: 0
# cmd: dir
# timestamp: 7/27/2025, 8:05:43 AM (1753628743805)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  904,326,815,744 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 155ad703-1ab9-448a-82d4-92ad9085baf6
# index: 0
# cmd: dir
# timestamp: 7/27/2025, 9:14:34 AM (1753632874920)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  894,860,009,472 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 4f51c5d4-41ff-47b9-98a4-3b685a68823b
# index: 1
# cmd: dir
# timestamp: 7/27/2025, 10:04:24 AM (1753635864872)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  894,596,448,256 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 347a6a87-acec-4359-abcd-f626bf0beb0f
# index: 0
# cmd: dir
# timestamp: 7/27/2025, 10:04:46 AM (1753635886589)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  894,594,760,704 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 2cbbbf31-fff6-455a-bae9-921680a62b61
# index: 1
# cmd: dir
# timestamp: 7/27/2025, 10:04:47 AM (1753635887374)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  894,594,752,512 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 4d50aa1d-80c3-47a7-be9b-548d705e01eb
# index: 0
# cmd: dir
# timestamp: 7/27/2025, 10:09:08 AM (1753636148242)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  895,777,542,144 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 058380e9-52f3-47d3-8435-5333667793ad
# index: 0
# cmd: dir
# timestamp: 7/27/2025, 10:09:13 AM (1753636153216)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  895,777,165,312 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: a5d86629-eb56-4c8b-8057-5222d8b80200
# index: 0
# cmd: dir
# timestamp: 7/27/2025, 10:09:19 AM (1753636159513)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  895,777,652,736 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 42403939-9558-4856-8173-144a98a1c77a
# index: 0
# cmd: dir
# timestamp: 7/27/2025, 10:09:24 AM (1753636164437)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  895,777,525,760 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 30d8cf18-5574-4cb7-a882-ec4043180af1
# index: 0
# cmd: dir
# timestamp: 7/27/2025, 10:09:29 AM (1753636169907)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  895,777,472,512 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 75501fe2-7971-4385-a5f8-2b75f120566d
# index: 0
# cmd: dir
# timestamp: 7/27/2025, 10:10:16 AM (1753636216850)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  895,776,485,376 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 27ffb088-589f-4b7a-99d6-fd6d4fa00006
# index: 0
# cmd: dir
# timestamp: 7/27/2025, 10:16:51 AM (1753636611414)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  916,672,352,256 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 28cc181b-8fc6-4f56-a3c8-69b2502979a2
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && git clone https://github.com/facefusion/facefusion-pinokio.git facefusion-pinokio.git
# timestamp: 7/27/2025, 10:16:57 AM (1753636617637)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\api>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && git clone https://github.com/facefusion/facefusion-pinokio.git facefusion-pinokio.git
Cloning into 'facefusion-pinokio.git'...
remote: Enumerating objects: 453, done.
remote: Counting objects: 100% (213/213), done.
remote: Compressing objects: 100% (78/78), done.
remote: Total 453 (delta 153), reused 144 (delta 135), pack-reused 240 (from 2)
Receiving objects: 100% (453/453), 324.69 KiB | 2.07 MiB/s, done.
Resolving deltas: 100% (283/283), done.

(base) C:\pinokio\api>

######################################################################
#
# group: undefined
# id: 2ed4718f-6f2b-4e10-bcba-9c64c64f9cf3
# index: 0
# cmd: dir
# timestamp: 7/27/2025, 10:17:07 AM (1753636627254)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  916,670,812,160 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 222ceb06-54eb-4f66-8b33-85c4d9f22694
# index: 0
# cmd: dir
# timestamp: 7/27/2025, 11:37:53 AM (1753641473492)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  912,603,103,232 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 2d01ddfc-65de-45eb-a51a-cfc347273325
# index: 0
# cmd: dir
# timestamp: 7/27/2025, 11:51:04 AM (1753642264121)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  912,598,982,656 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: eb2e4582-9c2f-4f4c-ac60-48571cb39eda
# index: 1
# cmd: dir
# timestamp: 7/29/2025, 11:39:50 PM (1753857590859)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  906,217,676,800 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 8e45a634-8953-4316-b8b8-8597987a4633
# index: 0
# cmd: dir
# timestamp: 7/29/2025, 11:39:52 PM (1753857592863)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  906,320,404,480 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 4f353897-cd19-457f-83a7-15bec64e20e3
# index: 0
# cmd: dir
# timestamp: 7/29/2025, 11:40:09 PM (1753857609577)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  906,318,282,752 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: e74f09ad-6f55-478c-ac4f-7a657fe7894a
# index: 0
# cmd: dir
# timestamp: 7/29/2025, 11:40:38 PM (1753857638684)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  906,327,711,744 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: d75f18f8-1958-429d-bb07-643901b71898
# index: 0
# cmd: dir
# timestamp: 7/29/2025, 11:41:24 PM (1753857684989)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  906,374,684,672 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 60e8faa0-3349-4c02-80d5-517ea6324973
# index: 0
# cmd: dir
# timestamp: 7/29/2025, 11:41:44 PM (1753857704527)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  906,374,623,232 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: e014af21-bfa0-4e38-8f71-18f933186b53
# index: 0
# cmd: dir
# timestamp: 7/29/2025, 11:41:52 PM (1753857712419)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  906,374,672,384 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 4432a3d6-2bfc-4c51-af1d-af20a178c723
# index: 0
# cmd: dir
# timestamp: 7/29/2025, 11:42:54 PM (1753857774756)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  912,251,072,512 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: ef6b06f0-06e8-45c3-9f01-d5b97b5557a3
# index: 0
# cmd: dir
# timestamp: 7/29/2025, 11:43:09 PM (1753857789466)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  912,252,071,936 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: cfe7f7e7-0c11-4564-bd4c-f26ee142b9f9
# index: 0
# cmd: dir
# timestamp: 7/29/2025, 11:59:44 PM (1753858784442)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  908,855,578,624 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 4f96447b-1c4e-43cb-91b8-fefafe27171b
# index: 1
# cmd: dir
# timestamp: 7/29/2025, 11:59:47 PM (1753858787973)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  908,855,558,144 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 21cd15c3-86d8-4c65-8d4f-d1fec991b299
# index: 0
# cmd: dir
# timestamp: 7/29/2025, 11:59:56 PM (1753858796691)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  908,855,406,592 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 8c340608-b02f-42e7-b58d-a791df1d9e9c
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && set
# timestamp: 8/1/2025, 9:38:58 PM (1754109538373)

Microsoft Windows [Version 10.0.22631.5624]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && set
ALLUSERSPROFILE=C:\ProgramData
APPDATA=C:\Users\<USER>\AppData\Roaming
ChocolateyInstall=C:\ProgramData\chocolatey
ChocolateyLastPathUpdate=133579645654484976
CommonProgramFiles=C:\Program Files\Common Files
CommonProgramW6432=C:\Program Files\Common Files
COMPUTERNAME=CONQUEST
ComSpec=C:\Windows\system32\cmd.exe
CONDA_DEFAULT_ENV=base
CONDA_PROMPT_MODIFIER=(base) 
CONDA_SHLVL=1
DriverData=C:\Windows\System32\Drivers\DriverData
EFC_7468=1
FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
FPS_BROWSER_USER_PROFILE_STRING=Default
GOOGLE_APPLICATION_CREDENTIALS=C:\Users\<USER>\OneDrive\Documents\Local Automation\eBay Dropshipping Automation Project\Google Sheets OAuth API Credentials\ebay-dropshipping-automation-sa-credentials-df490ffa2b33.json
GROQ_API_KEY=********************************************************
HOMEDRIVE=C:
HOMEPATH=\Users\ullin
LOCALAPPDATA=C:\Users\<USER>\AppData\Local
LOGONSERVER=\\CONQUEST
MSMPI_BENCHMARKS=C:\Program Files\Microsoft MPI\Benchmarks\
MSMPI_BIN=C:\Program Files\Microsoft MPI\Bin\
NUMBER_OF_PROCESSORS=24
OculusBase=C:\Program Files\Oculus\
OneDrive=C:\Users\<USER>\OneDrive
OneDriveConsumer=C:\Users\<USER>\OneDrive
ORIGINAL_XDG_CURRENT_DESKTOP=undefined
OS=Windows_NT
Path=C:\pinokio\bin\miniconda;C:\pinokio\bin\miniconda\Library\mingw64\bin;C:\pinokio\bin\miniconda\Library\mingw-w64\bin;C:\pinokio\bin\miniconda\Library\usr\bin;C:\pinokio\bin\miniconda\Library\bin;C:\pinokio\bin\miniconda\Scripts;C:\pinokio\bin\miniconda\bin;C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\etc\profile.d;C:\pinokio\bin\miniconda\bin;C:\pinokio\bin\miniconda\Scripts;C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\lib;C:\pinokio\bin\miniconda\Library\bin;C:\pinokio\bin\miniconda\pkgs;C:\pinokio\bin\miniconda;C:\pinokio\bin\homebrew\bin;C:\pinokio\bin\homebrew\Cellar;C:\pinokio\bin\aria2;C:\Windows\System32;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\libnvvp;C:\Program Files\Microsoft MPI\Bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files\dotnet;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\ExifTool;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;C:\ProgramData\chocolatey\bin;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.1.1;C:\Users\<USER>\miniconda3;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.39.33519\bin\Hostx64\x64;C:\Users\<USER>\AppData\Local\NVIDIA\ChatRTX\env_nvd_rag\Lib\site-packages\torch\lib;C:\Program Files\Git\cmd;C:\Program Files\Inkscape\bin;C:\Program Files\FFmpeg\ffmpeg-n7.0-latest-win64-gpl-7.0\bin;.;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\nodejs;C:\Windows\system32\config\systemprofile\AppData\Local\Muse Hub\lib;C:\Users\<USER>\.local\share\solana\install\active_release\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Local\Programs\mongosh;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\pinokio\bin
PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC
PLAYWRIGHT_BROWSERS_PATH=C:\pinokio\bin\playwright\browsers
PROCESSOR_ARCHITECTURE=AMD64
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
PROCESSOR_LEVEL=6
PROCESSOR_REVISION=b701
ProgramData=C:\ProgramData
ProgramFiles=C:\Program Files
ProgramFiles(x86)=C:\Program Files (x86)
ProgramW6432=C:\Program Files
PROMPT=(base) $P$G
PSModulePath=C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC=C:\Users\<USER>\pinokio\bin\miniconda\Library\ssl\certs
SSL_CERT_FILE=C:\pinokio\bin\miniconda\Library\ssl\cacert.pem
SystemDrive=C:
SystemRoot=C:\Windows
TEMP=C:\pinokio\cache\TEMP
TMP=C:\pinokio\cache\TMP
USERDOMAIN=CONQUEST
USERDOMAIN_ROAMINGPROFILE=CONQUEST
USERNAME=ullin
USERPROFILE=C:\Users\<USER>\Program Files\Oracle\VirtualBox\
windir=C:\Windows
CMAKE_OBJECT_PATH_MAX=1024
PYTORCH_ENABLE_MPS_FALLBACK=1
TORCH_FORCE_NO_WEIGHTS_ONLY_LOAD=1
HOMEBREW_CACHE=C:\pinokio\bin\homebrew\cache
XDG_CACHE_HOME=C:\pinokio\cache\XDG_CACHE_HOME
PIP_CACHE_DIR=C:\pinokio\cache\PIP_CACHE_DIR
UV_CACHE_DIR=C:\pinokio\cache\UV_CACHE_DIR
PIP_TMPDIR=C:\pinokio\cache\PIP_TMPDIR
TMPDIR=C:\pinokio\cache\TMPDIR
XDG_DATA_HOME=C:\pinokio\cache\XDG_DATA_HOME
XDG_CONFIG_HOME=C:\pinokio\cache\XDG_CONFIG_HOME
XDG_STATE_HOME=C:\pinokio\cache\XDG_STATE_HOME
PIP_CONFIG_FILE=C:\pinokio\pipconfig
CONDARC=C:\pinokio\condarc
PS1=<<PINOKIO_SHELL>>
GRADIO_ANALYTICS_ENABLED=False
GRADIO_ALLOWED_PATHS=C:\pinokio
PINOKIO_SHARE_VAR=url
PINOKIO_SHARE_CLOUDFLARE=false
PINOKIO_SCRIPT_DEFAULT=true
PINOKIO_DRIVE=C:\pinokio\drive
GRADIO_TEMP_DIR=C:\pinokio\cache\GRADIO_TEMP_DIR
HF_HOME=C:\pinokio\cache\HF_HOME
TORCH_HOME=C:\pinokio\cache\TORCH_HOME
PINOKIO_SHARE_LOCAL=false
HOMEBREW_PREFIX=C:\pinokio\bin\homebrew
HOMEBREW_CELLAR=C:\pinokio\bin\homebrew\Cellar
HOMEBREW_REPOSITORY=C:\pinokio\bin\homebrew
CONDA_PREFIX=C:\pinokio\bin\miniconda
CONDA_ENVS_PATH=C:\pinokio\bin\miniconda\envs
CONDA_PKGS_DIRS=C:\pinokio\bin\miniconda\pkgs
PYTHON=C:\pinokio\bin\miniconda\python
CONDA_BAT=C:\pinokio\bin\miniconda\condabin\conda.bat
CONDA_EXE=C:\pinokio\bin\miniconda\Scripts\conda.exe
CONDA_PYTHON_EXE=C:\pinokio\bin\miniconda\python.exe
CUDA_HOME=C:\pinokio\bin\miniconda
GIT_CONFIG_GLOBAL=C:\pinokio\gitconfig
CONDA_AUTO_ACTIVATE_BASE=false
PYTHONNOUSERSITE=1
PIP_REQUIRE_VIRTUALENV=true
UV_PYTHON_PREFERENCE=only-managed
__CONDA_OPENSSL_CERT_DIR_SET=1
__CONDA_OPENSSL_CERT_FILE_SET=1

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 10b8b085-f28a-4e32-a901-34e334dc14f8
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 8/1/2025, 9:39:02 PM (1754109542316)

Microsoft Windows [Version 10.0.22631.5624]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# packages in environment at C:\pinokio\bin\miniconda:
#
# Name                        Version          Build              Channel
7zip                          24.08            h49e36cd_1         conda-forge
_python_abi3_support          1.0              hd8ed1ab_2         conda-forge
anaconda-anon-usage           0.5.0            py310hfc23b7f_100
anaconda_powershell_prompt    1.1.0            haa95532_0
anaconda_prompt               1.1.0            haa95532_0
annotated-types               0.6.0            py310haa95532_0
archspec                      0.2.3            pyhd3eb1b0_0
boltons                       24.1.0           py310haa95532_0
brotli-python                 1.0.9            py310h5da7b33_9
bzip2                         1.0.8            h2bbff1b_6
ca-certificates               2025.7.14        h4c7d964_0         conda-forge
certifi                       2025.7.14        pyhd8ed1ab_0       conda-forge
cffi                          1.17.1           py310h827c3e9_1
charset-normalizer            3.3.2            pyhd3eb1b0_0
colorama                      0.4.6            py310haa95532_0
conda                         25.5.1           py310h5588dad_0    conda-forge
conda-anaconda-telemetry      0.1.2            py310haa95532_0
conda-anaconda-tos            0.1.2            py310haa95532_0
conda-content-trust           0.2.0            py310haa95532_1
conda-libmamba-solver         25.1.1           pyhd3eb1b0_0
conda-package-handling        2.4.0            py310haa95532_0
conda-package-streaming       0.11.0           py310haa95532_0
cpp-expected                  1.1.0            h214f63a_0
cpython                       3.10.18          py310hd8ed1ab_0    conda-forge
cryptography                  43.0.3           py310hbd6ee87_1
cuda                          12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-cccl                     12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-command-line-tools       12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-compiler                 12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-cudart                   12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-cudart-dev               12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-cuobjdump                12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-cupti                    12.1.62          0                  nvidia/label/cuda-12.1.0
cuda-cuxxfilt                 12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-demo-suite               12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-documentation            12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-libraries                12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-libraries-dev            12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-nsight-compute           12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-nvcc                     12.1.66          0                  nvidia/label/cuda-12.1.0
cuda-nvdisasm                 12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-nvml-dev                 12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-nvprof                   12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-nvprune                  12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-nvrtc                    12.9.86          he0c23c2_0         conda-forge
cuda-nvrtc-dev                12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-nvtx                     12.1.66          0                  nvidia/label/cuda-12.1.0
cuda-nvvp                     12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-opencl                   12.1.56          0                  nvidia/label/cuda-12.1.0
cuda-opencl-dev               12.1.56          0                  nvidia/label/cuda-12.1.0
cuda-profiler-api             12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-runtime                  12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-sanitizer-api            12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-toolkit                  12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-tools                    12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-version                  12.9             h4f385c5_3         conda-forge
cuda-visual-tools             12.1.0           0                  nvidia/label/cuda-12.1.0
cudnn                         9.10.1.4         h32ff316_1         conda-forge
distro                        1.9.0            py310haa95532_0
ffmpeg                        4.3.1            ha925a31_0         conda-forge
filelock                      3.18.0           pyhd8ed1ab_0       conda-forge
fmt                           9.1.0            h6d14046_1
frozendict                    2.4.2            py310h2bbff1b_0
fsspec                        2025.7.0         pyhd8ed1ab_0       conda-forge
git                           2.49.0           h57928b3_2         conda-forge
git-lfs                       3.7.0            h86e1c39_0         conda-forge
hf-xet                        1.1.5            py39h17685eb_3     conda-forge
huggingface_hub               0.33.4           pyhd8ed1ab_0       conda-forge
idna                          3.7              py310haa95532_0
jsonpatch                     1.33             py310haa95532_1
jsonpointer                   2.1              pyhd3eb1b0_0
libarchive                    3.7.7            h9243413_0
libcublas                     12.9.1.4         he0c23c2_0         conda-forge
libcublas-dev                 12.1.0.26        0                  nvidia/label/cuda-12.1.0
libcudnn                      9.10.1.4         hca898b4_1         conda-forge
libcudnn-dev                  9.10.1.4         hca898b4_1         conda-forge
libcufft                      11.0.2.4         0                  nvidia/label/cuda-12.1.0
libcufft-dev                  11.0.2.4         0                  nvidia/label/cuda-12.1.0
libcurand                     10.3.2.56        0                  nvidia/label/cuda-12.1.0
libcurand-dev                 10.3.2.56        0                  nvidia/label/cuda-12.1.0
libcurl                       8.11.1           haff574d_0
libcusolver                   11.4.4.55        0                  nvidia/label/cuda-12.1.0
libcusolver-dev               11.4.4.55        0                  nvidia/label/cuda-12.1.0
libcusparse                   12.0.2.55        0                  nvidia/label/cuda-12.1.0
libcusparse-dev               12.0.2.55        0                  nvidia/label/cuda-12.1.0
libffi                        3.4.4            hd77b12b_1
libiconv                      1.16             h2bbff1b_3
libmamba                      2.0.5            hcd6fe79_1
libmambapy                    2.0.5            py310h214f63a_1
libnpp                        12.0.2.50        0                  nvidia/label/cuda-12.1.0
libnpp-dev                    12.0.2.50        0                  nvidia/label/cuda-12.1.0
libnvjitlink                  12.1.55          0                  nvidia/label/cuda-12.1.0
libnvjitlink-dev              12.1.55          0                  nvidia/label/cuda-12.1.0
libnvjpeg                     12.1.0.39        0                  nvidia/label/cuda-12.1.0
libnvjpeg-dev                 12.1.0.39        0                  nvidia/label/cuda-12.1.0
libnvvm-samples               12.1.55          0                  nvidia/label/cuda-12.1.0
libsolv                       0.7.30           hf2fb9eb_1
libsqlite                     3.47.2           h67fdade_0         conda-forge
libssh2                       1.11.1           h2addb87_0
libxml2                       2.13.5           h24da03e_0
libzlib                       1.2.13           h2466b09_6         conda-forge
libzlib-wapi                  1.2.13           h2466b09_6         conda-forge
lz4-c                         1.9.4            h2bbff1b_1
markdown-it-py                2.2.0            py310haa95532_1
mdurl                         0.1.0            py310haa95532_0
menuinst                      2.2.0            py310h5da7b33_1
nlohmann_json                 3.11.2           h6c2663c_0
nodejs                        24.4.1           he453025_0         conda-forge
nsight-compute                2023.1.0.15      0                  nvidia/label/cuda-12.1.0
openssl                       3.5.1            h725018a_0         conda-forge
packaging                     24.2             py310haa95532_0
pcre2                         10.42            h0ff8eda_1
pip                           25.0             py310haa95532_0
platformdirs                  3.10.0           py310haa95532_0
pluggy                        1.5.0            py310haa95532_0
pnpm                          10.13.1          h785286a_1         conda-forge
pybind11-abi                  5                hd3eb1b0_0
pycosat                       0.6.6            py310h827c3e9_2
pycparser                     2.21             pyhd3eb1b0_0
pydantic                      2.10.3           py310haa95532_0
pydantic-core                 2.27.1           py310h636fa0f_0
pygments                      2.15.1           py310haa95532_1
pysocks                       1.7.1            py310haa95532_0
python                        3.10.16          h4607a30_1
python-gil                    3.10.18          hd8ed1ab_0         conda-forge
python_abi                    3.10             2_cp310            conda-forge
pyyaml                        6.0.2            py310h38315fa_2    conda-forge
reproc                        14.2.4           hd77b12b_2
reproc-cpp                    14.2.4           hd77b12b_2
requests                      2.32.3           py310haa95532_1
rich                          13.9.4           py310haa95532_0
ruamel.yaml                   0.18.6           py310h827c3e9_0
ruamel.yaml.clib              0.2.8            py310h827c3e9_0
setuptools                    75.8.0           py310haa95532_0
simdjson                      3.10.1           h214f63a_0
spdlog                        1.11.0           h59b6b97_0
sqlite                        3.47.2           h2466b09_0         conda-forge
tk                            8.6.14           h0416ee5_0
tqdm                          4.67.1           py310h9909e9c_0
truststore                    0.10.0           py310haa95532_0
typing-extensions             4.12.2           py310haa95532_0
typing_extensions             4.12.2           py310haa95532_0
tzdata                        2025a            h04d1e81_0
ucrt                          10.0.22621.0     h57928b3_1         conda-forge
urllib3                       2.3.0            py310haa95532_0
uv                            0.8.3            h579f82e_0         conda-forge
vc                            14.42            haa95532_4
vc14_runtime                  14.44.35208      h818238b_30        conda-forge
vs2015_runtime                14.44.35208      h38c0c73_30        conda-forge
wheel                         0.45.1           py310haa95532_0
win_inet_pton                 1.1.0            py310haa95532_0
xz                            5.4.6            h8cc25b3_1
yaml                          0.2.5            h6a83c73_3         conda-forge
yaml-cpp                      0.8.0            hd77b12b_1
zlib                          1.2.13           h2466b09_6         conda-forge
zstandard                     0.23.0           py310h4fc1ca9_1
zstd                          1.5.6            h8880b57_0

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 9ca4a0e9-6ced-4f17-a580-2bd42614f210
# index: 0
# cmd: reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled
# timestamp: 8/1/2025, 9:39:03 PM (1754109543575)

Microsoft Windows [Version 10.0.22631.5624]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled

HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\FileSystem
    LongPathsEnabled    REG_DWORD    0x1


C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 7db09e2c-812f-490d-a370-c3d2943390cc
# index: 0
# cmd: dir
# timestamp: 8/1/2025, 9:39:04 PM (1754109544826)

Microsoft Windows [Version 10.0.22631.5624]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  922,558,816,256 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 8f49d7cb-1b94-4ed2-9c55-dd707ad61f03
# index: 0
# cmd: dir
# timestamp: 8/1/2025, 9:39:14 PM (1754109554841)

Microsoft Windows [Version 10.0.22631.5624]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  922,556,690,432 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: ec59ec5e-f7f9-4426-9df9-755061b3439e
# index: 1
# cmd: dir
# timestamp: 8/1/2025, 10:35:17 PM (1754112917515)

Microsoft Windows [Version 10.0.22631.5624]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  922,049,163,264 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 93231005-0649-43b7-9060-654590a9f05a
# index: 0
# cmd: dir
# timestamp: 8/1/2025, 10:35:31 PM (1754112931563)

Microsoft Windows [Version 10.0.22631.5624]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  922,048,970,752 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 796033b0-4e37-4845-857d-ad698b097687
# index: 0
# cmd: dir
# timestamp: 8/2/2025, 12:10:52 AM (1754118652778)

Microsoft Windows [Version 10.0.22631.5624]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  921,751,592,960 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 6a17d3c3-d61b-4df7-895b-7d851bae71b0
# index: 0
# cmd: dir
# timestamp: 8/2/2025, 12:10:59 AM (1754118659279)

Microsoft Windows [Version 10.0.22631.5624]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  921,751,592,960 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 03eb937d-a272-41da-852e-5df1840192ce
# index: 0
# cmd: dir
# timestamp: 8/2/2025, 12:45:31 AM (1754120731699)

Microsoft Windows [Version 10.0.22631.5624]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  921,714,552,832 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: f996cb0c-0d85-4d34-9313-2ac023b5c1fb
# index: 0
# cmd: dir
# timestamp: 8/2/2025, 12:45:48 AM (1754120748832)

Microsoft Windows [Version 10.0.22631.5624]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  921,714,925,568 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 2be609c8-836a-46fe-8802-6955c4c91509
# index: 1
# cmd: dir
# timestamp: 8/2/2025, 1:22:07 AM (1754122927110)

Microsoft Windows [Version 10.0.22631.5624]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  921,689,034,752 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: cdfb48e7-a490-4a6e-8dec-5e08cb553b66
# index: 0
# cmd: dir
# timestamp: 8/2/2025, 1:22:08 AM (1754122928547)

Microsoft Windows [Version 10.0.22631.5624]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  921,685,278,720 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: c4ddac78-56b8-4b42-b41e-782179eba023
# index: 1
# cmd: dir
# timestamp: 8/2/2025, 1:22:20 AM (1754122940979)

Microsoft Windows [Version 10.0.22631.5624]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  921,689,907,200 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 6f1d6f16-21bb-4a2b-b77a-7260c7fcd0a5
# index: 0
# cmd: dir
# timestamp: 8/2/2025, 1:22:27 AM (1754122947738)

Microsoft Windows [Version 10.0.22631.5624]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  921,687,556,096 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: c0121e08-8fc4-4770-af9d-c9ce38fb751a
# index: 1
# cmd: dir
# timestamp: 8/2/2025, 11:26:44 AM (1754159204175)

Microsoft Windows [Version 10.0.22631.5624]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  922,116,067,328 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 8ffdddf3-efa8-4bac-9c38-38a97f3225bd
# index: 0
# cmd: dir
# timestamp: 8/2/2025, 11:26:54 AM (1754159214628)

Microsoft Windows [Version 10.0.22631.5624]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  922,116,915,200 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 9f768c25-da85-42c9-8b68-d2c1821cc60a
# index: 0
# cmd: dir
# timestamp: 8/2/2025, 11:27:34 AM (1754159254107)

Microsoft Windows [Version 10.0.22631.5624]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  922,077,995,008 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 14d2b874-9601-46ed-92e9-0c3e432651a6
# index: 0
# cmd: dir
# timestamp: 8/2/2025, 11:27:52 AM (1754159272296)

Microsoft Windows [Version 10.0.22631.5624]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/26/2025  10:01 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  922,079,506,432 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

