.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_session_get_read_timeout 3 "13 Jan 2023" "libssh2" "libssh2"
.SH NAME
libssh2_session_get_read_timeout - get the timeout for packet read functions
.SH SYNOPSIS
.nf
#include <libssh2.h>

long
libssh2_session_get_read_timeout(LIBSSH2_SESSION *session);
.fi
.SH DESCRIPTION
Returns the \fBtimeout\fP (in seconds) for how long the ssh2 packet receive
function calls may wait until they consider the situation an error and
return LIBSSH2_ERROR_TIMEOUT.

By default the timeout is 60 seconds.
.SH RETURN VALUE
The value of the timeout setting.
.SH <PERSON>VAILABILITY
Added in 1.10.1
.SH SEE ALSO
.BR libssh2_session_set_read_timeout(3)
