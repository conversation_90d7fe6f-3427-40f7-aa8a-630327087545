.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_init 3 "19 Mar 2010" "libssh2" "libssh2"
.SH NAME
libssh2_init - global library initialization
.SH SYNOPSIS
.nf
#include <libssh2.h>

#define LIBSSH2_INIT_NO_CRYPTO 0x0001

int
libssh2_init(int flags);
.fi
.SH DESCRIPTION
Initialize the libssh2 functions. This typically initialize the
crypto library. It uses a global state, and is not thread safe -- you
must make sure this function is not called concurrently.
.SH RETURN VALUE
Returns 0 if succeeded, or a negative value for error.
.SH AVAILABILITY
Added in libssh2 1.2.5
.SH SEE ALSO
.BR libssh2_exit(3)
