.\" Copyright (C) <PERSON>
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_agent_set_identity_path 3 "6 Mar 2019" "libssh2" "libssh2"
.SH NAME
libssh2_agent_set_identity_path - set an ssh-agent socket path on disk
.SH SYNOPSIS
.nf
#include <libssh2.h>

void
libssh2_agent_set_identity_path(LIBSSH2_AGENT *agent, const char *path);
.fi
.SH DESCRIPTION
Allows a custom agent identity socket path instead of the default SSH_AUTH_SOCK env value

.SH RETURN VALUE
Returns void
.SH AVAILABILITY
Added in libssh2 1.9
.SH SEE ALSO
.BR libssh2_agent_init(3)
.BR libssh2_agent_get_identity_path(3)
