{"paths": [{"_path": "Lib/site-packages/menuinst-2.2.0.dist-info/AUTHORS.md", "path_type": "hardlink", "sha256": "c92db3cc791aee280bf2dee7ec6269b6f083595c75f4b2f48b3c552358720ff4", "size_in_bytes": 638}, {"_path": "Lib/site-packages/menuinst-2.2.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/menuinst-2.2.0.dist-info/LICENSE.txt", "path_type": "hardlink", "sha256": "911b693bb34494db6bd2c24f591674633fe99b8bc1442bc3fcf5121a0a82fae5", "size_in_bytes": 1530}, {"_path": "Lib/site-packages/menuinst-2.2.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "58e7165b798331701b93d605037203c8cf795772c83d94bf804f8a78af8ca694", "size_in_bytes": 4915}, {"_path": "Lib/site-packages/menuinst-2.2.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "75d5fbe6dec9b402c0a5c74f5af640d1504d729f1dc2606a1a766509cd202e77", "size_in_bytes": 4892}, {"_path": "Lib/site-packages/menuinst-2.2.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/menuinst-2.2.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "af319f66051c19e2928081c660cbaa838c44e153c7c676a57571fa046d338d59", "size_in_bytes": 101}, {"_path": "Lib/site-packages/menuinst-2.2.0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "155f19d167cd2470f6a8b3964a059f30e55eb19189a8b1f1470ceb73474a5ddb", "size_in_bytes": 88}, {"_path": "Lib/site-packages/menuinst-2.2.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "f6f1c69885158f9dacec991ad444ddb401bd2d644232a5f9151ae5391de7873e", "size_in_bytes": 9}, {"_path": "Lib/site-packages/menuinst/__init__.py", "path_type": "hardlink", "sha256": "57046d6d64124f3d8fe2427ffbebe212bd292515a32f277568bb25b64f2b1460", "size_in_bytes": 1537}, {"_path": "Lib/site-packages/menuinst/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "258c6f11094b9a134227087695a232fa82afac93c126c60182dac7028ab61542", "size_in_bytes": 1154}, {"_path": "Lib/site-packages/menuinst/__pycache__/_schema.cpython-310.pyc", "path_type": "hardlink", "sha256": "3edf8b01b6adcf9b89f52439c8c487197b41ae7a3924152d5808ccf5c4234cf0", "size_in_bytes": 10551}, {"_path": "Lib/site-packages/menuinst/__pycache__/_version.cpython-310.pyc", "path_type": "hardlink", "sha256": "1923810c77dc758708cb849f30dc552ba8d072444e5455151bc6af4850ee5da4", "size_in_bytes": 454}, {"_path": "Lib/site-packages/menuinst/__pycache__/api.cpython-310.pyc", "path_type": "hardlink", "sha256": "82b07b635b14b284699f5b5e27cfa9b66a1c6b3f521026060ce9b3155cb5c44b", "size_in_bytes": 4384}, {"_path": "Lib/site-packages/menuinst/__pycache__/utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "22f62ff3c804ccae7c76d1cafef27666b86103fe96b3e32e8407e199e4c301da", "size_in_bytes": 12084}, {"_path": "Lib/site-packages/menuinst/_legacy/__init__.py", "path_type": "hardlink", "sha256": "d60d57e8085f4dfdf5f55bcd13c9367c7930f06a988d2e2b9e5da85e43101b9e", "size_in_bytes": 2879}, {"_path": "Lib/site-packages/menuinst/_legacy/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "22b7d14263e98d8df8473efa9f2c7fb9620f5e3eccd18c9bfe8759c262596367", "size_in_bytes": 2230}, {"_path": "Lib/site-packages/menuinst/_legacy/__pycache__/cwp.cpython-310.pyc", "path_type": "hardlink", "sha256": "108d7d891152bb3fa71a8300394b6f4f887dbcd6cd025a7a39b5fa0938528c97", "size_in_bytes": 1411}, {"_path": "Lib/site-packages/menuinst/_legacy/__pycache__/main.cpython-310.pyc", "path_type": "hardlink", "sha256": "a7e5e7f6d5dc03ad4033df62f2604330b9b5f510ec7241d9eee1da91b77c493b", "size_in_bytes": 912}, {"_path": "Lib/site-packages/menuinst/_legacy/__pycache__/utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "614c4e4b9964080459b71b849c22da411de5eea38717e2e2d1fefefb62b49aab", "size_in_bytes": 609}, {"_path": "Lib/site-packages/menuinst/_legacy/__pycache__/win32.cpython-310.pyc", "path_type": "hardlink", "sha256": "ccd71410dd2f46b1874dd5fd3cfd872a57148ccfe0c8c48d29b74d88da6e9954", "size_in_bytes": 8128}, {"_path": "Lib/site-packages/menuinst/_legacy/cwp.py", "path_type": "hardlink", "sha256": "137c278ebebdb1e2b5993d8c0b5902e2ae18425c816275f45c5b3a79b15c47ec", "size_in_bytes": 1873}, {"_path": "Lib/site-packages/menuinst/_legacy/main.py", "path_type": "hardlink", "sha256": "21dcf0c60356accc82063a7f1f3c4525681539d7a700dbee7a0eb2b28eaf08b6", "size_in_bytes": 693}, {"_path": "Lib/site-packages/menuinst/_legacy/utils.py", "path_type": "hardlink", "sha256": "654768af0a358adbf8df074b003c954cfdfa888077ce0a1d622adfe034baced2", "size_in_bytes": 522}, {"_path": "Lib/site-packages/menuinst/_legacy/win32.py", "path_type": "hardlink", "sha256": "48c6c2f997ba3a7c017c13abd946fd97d7b87599dbdcdbf1c3780d84583e63a3", "size_in_bytes": 11169}, {"_path": "Lib/site-packages/menuinst/_schema.py", "path_type": "hardlink", "sha256": "97dd5351cf9afd5b7365ca8cf3943b82e32e93f15b961349a84991b29285f84c", "size_in_bytes": 19247}, {"_path": "Lib/site-packages/menuinst/_vendor/apipkg/LICENSE", "path_type": "hardlink", "sha256": "e89eed1074d3a943198ba139b80844f5b445b860bba74a8aeadc0610566164ea", "size_in_bytes": 1054}, {"_path": "Lib/site-packages/menuinst/_vendor/apipkg/__init__.py", "path_type": "hardlink", "sha256": "4d23679f7be7ba1bcee076c1e9fddee0a8e9571907c954def9116243bf8b67a5", "size_in_bytes": 1123}, {"_path": "Lib/site-packages/menuinst/_vendor/apipkg/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "ebb21bccc636bb9893b031d14e22e22a618892718284fbfa641ebfc973458dc0", "size_in_bytes": 1227}, {"_path": "Lib/site-packages/menuinst/_vendor/apipkg/__pycache__/_alias_module.cpython-310.pyc", "path_type": "hardlink", "sha256": "55e5684fd39df09583343a794ae4fcf185a9d54a49290afe5ddb57c154192564", "size_in_bytes": 1956}, {"_path": "Lib/site-packages/menuinst/_vendor/apipkg/__pycache__/_importing.cpython-310.pyc", "path_type": "hardlink", "sha256": "412a2590e4c1760a91c61a1eb6bf424c66d3eebedd700399bb909efd3fbfa40f", "size_in_bytes": 1340}, {"_path": "Lib/site-packages/menuinst/_vendor/apipkg/__pycache__/_module.cpython-310.pyc", "path_type": "hardlink", "sha256": "4020316e6f92e725fad97ca41deaddaf9e54912094e702f20567af76e6cc7156", "size_in_bytes": 5185}, {"_path": "Lib/site-packages/menuinst/_vendor/apipkg/__pycache__/_syncronized.cpython-310.pyc", "path_type": "hardlink", "sha256": "f41b9bdae5bbc0c0ed336d9dee6857f4bc317bee8354d1707e703cc229a4c67e", "size_in_bytes": 700}, {"_path": "Lib/site-packages/menuinst/_vendor/apipkg/__pycache__/_version.cpython-310.pyc", "path_type": "hardlink", "sha256": "f58b5460b23e603694c5a54af5a41fdc8f378e42172b46cd8e93c0c8e28f1404", "size_in_bytes": 201}, {"_path": "Lib/site-packages/menuinst/_vendor/apipkg/_alias_module.py", "path_type": "hardlink", "sha256": "19ae291945defd417373822dacd99831459cf460b862dbf9b0737d40f9da5836", "size_in_bytes": 1186}, {"_path": "Lib/site-packages/menuinst/_vendor/apipkg/_importing.py", "path_type": "hardlink", "sha256": "25fed44677085569ff50163977555af3ee54f52edfa007b34a6756c781c0ccfa", "size_in_bytes": 1067}, {"_path": "Lib/site-packages/menuinst/_vendor/apipkg/_module.py", "path_type": "hardlink", "sha256": "bd5dfabd1654a6004efe8e70476e2e7d308cfc20aa2993c6640e8fdc5c07d5d1", "size_in_bytes": 6897}, {"_path": "Lib/site-packages/menuinst/_vendor/apipkg/_syncronized.py", "path_type": "hardlink", "sha256": "d46bfe8b2292eed31e1ed6ba55d62b27b8f23155380403fef745a274b827a577", "size_in_bytes": 484}, {"_path": "Lib/site-packages/menuinst/_vendor/apipkg/_version.py", "path_type": "hardlink", "sha256": "03ba5eeb031f9c58d3c50b27ae22d5f3f25d6dbb8192b377567c8afa54be4c10", "size_in_bytes": 142}, {"_path": "Lib/site-packages/menuinst/_vendor/apipkg/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/menuinst/_version.py", "path_type": "hardlink", "sha256": "2ba9f8ceb0dc6d389549ced127424e1f880ca31e51a9592809950794497d2eb5", "size_in_bytes": 427}, {"_path": "Lib/site-packages/menuinst/api.py", "path_type": "hardlink", "sha256": "3a5a8fe9c8a7f7fcbd6568d9429411f9407d0b95ab692b1573899a391d8b6132", "size_in_bytes": 5566}, {"_path": "Lib/site-packages/menuinst/data/menuinst.default.json", "path_type": "hardlink", "sha256": "c456f6e588658a88eaa0b01067b8523cced635bff644b0b5017b904302e9eccd", "size_in_bytes": 2000}, {"_path": "Lib/site-packages/menuinst/data/menuinst.schema.json", "path_type": "hardlink", "sha256": "f223a891a0e91d7a95067312b19ae8ef8d64628b32f754ff0baef1d7859edd66", "size_in_bytes": 19053}, {"_path": "Lib/site-packages/menuinst/platforms/__init__.py", "path_type": "hardlink", "sha256": "2aceb9cbf779fd2b9e023c06dbc861f5ab016620052aaecfddbe4c7cf31da811", "size_in_bytes": 744}, {"_path": "Lib/site-packages/menuinst/platforms/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "5791e50c6e883a88b202940763ea03a5ee3564441f1b7a474cd209ce91902c5e", "size_in_bytes": 852}, {"_path": "Lib/site-packages/menuinst/platforms/__pycache__/base.cpython-310.pyc", "path_type": "hardlink", "sha256": "7558a58f8dfc410ac10e6ffe66b48c761807ecde18257343dcc7b95f68e8ea2f", "size_in_bytes": 9133}, {"_path": "Lib/site-packages/menuinst/platforms/__pycache__/linux.cpython-310.pyc", "path_type": "hardlink", "sha256": "c859079cc058c79115c917d202965c2b6fc74a52e5767b380eaf4deafd9e8585", "size_in_bytes": 13338}, {"_path": "Lib/site-packages/menuinst/platforms/__pycache__/osx.cpython-310.pyc", "path_type": "hardlink", "sha256": "44ed937f5cccc44c55c182cd70f51f943c96115eff5450388329ad4aaab91754", "size_in_bytes": 11748}, {"_path": "Lib/site-packages/menuinst/platforms/__pycache__/win.cpython-310.pyc", "path_type": "hardlink", "sha256": "49b1611f92944d10fd2bf7668f6e83e80ee98afcc9ad379178f690d3fa3081b5", "size_in_bytes": 16892}, {"_path": "Lib/site-packages/menuinst/platforms/base.py", "path_type": "hardlink", "sha256": "23b21bb3293064d3dfca04fd162546d67e842ba33a23985f5d9019b2f34f2b07", "size_in_bytes": 9112}, {"_path": "Lib/site-packages/menuinst/platforms/linux.py", "path_type": "hardlink", "sha256": "2f65b513827ac4dd5b30118dca103676af82681d012bdcd472b64ef8883f0a22", "size_in_bytes": 16210}, {"_path": "Lib/site-packages/menuinst/platforms/osx.py", "path_type": "hardlink", "sha256": "8e9513299c4448c9965b30819f942fd761471038940be4357d3a83b1575e6316", "size_in_bytes": 13253}, {"_path": "Lib/site-packages/menuinst/platforms/win.py", "path_type": "hardlink", "sha256": "1af250fe85dfad42a8e02c45d427594c09093e926de7869eb33315110e84451e", "size_in_bytes": 20081}, {"_path": "Lib/site-packages/menuinst/platforms/win_utils/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/menuinst/platforms/win_utils/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "5ad24f770c6b21e23dbf1c67482d10e777337d87dbd0fd17b4e5b6e02aecdbe6", "size_in_bytes": 151}, {"_path": "Lib/site-packages/menuinst/platforms/win_utils/__pycache__/knownfolders.cpython-310.pyc", "path_type": "hardlink", "sha256": "7bd0b300f7c12013f2f6315c196460329622717d20d29d52558de96784659700", "size_in_bytes": 12351}, {"_path": "Lib/site-packages/menuinst/platforms/win_utils/__pycache__/registry.cpython-310.pyc", "path_type": "hardlink", "sha256": "149dfac7e084da3434510e2123406689854d43ce85247de845c738ab30764597", "size_in_bytes": 6482}, {"_path": "Lib/site-packages/menuinst/platforms/win_utils/__pycache__/win_elevate.cpython-310.pyc", "path_type": "hardlink", "sha256": "7a2b02e6e4a4ec4dc6d8325eb1911116fef693fa47bd217a8a71f14f87552d29", "size_in_bytes": 3908}, {"_path": "Lib/site-packages/menuinst/platforms/win_utils/knownfolders.py", "path_type": "hardlink", "sha256": "64607e93621ec4d3a4d8afb244218c1791f3c6d4be1aa1be9482db694dbdb129", "size_in_bytes": 15303}, {"_path": "Lib/site-packages/menuinst/platforms/win_utils/registry.py", "path_type": "hardlink", "sha256": "6fd69a7bf8b6ed5b44991068308292490e9f07e842d0d0db1f053895f61c4cb2", "size_in_bytes": 7872}, {"_path": "Lib/site-packages/menuinst/platforms/win_utils/win_elevate.py", "path_type": "hardlink", "sha256": "f4191910af32e313d5964acebe311e1e64cb77b7c5f420a348e4e5ba3dbcd02d", "size_in_bytes": 5068}, {"_path": "Lib/site-packages/menuinst/platforms/win_utils/winshortcut.cp310-win_amd64.pyd", "path_type": "hardlink", "sha256": "b620fb7a007465aa87d34b576723344655793067bb007023af23e19f57b0c8d3", "size_in_bytes": 25360}, {"_path": "Lib/site-packages/menuinst/utils.py", "path_type": "hardlink", "sha256": "432a337ae40836630adc5a2c6ce79d68f993c67c7f652fd6d9e699c6e77e2f96", "size_in_bytes": 17837}, {"_path": "cwp.py", "path_type": "hardlink", "sha256": "137c278ebebdb1e2b5993d8c0b5902e2ae18425c816275f45c5b3a79b15c47ec", "size_in_bytes": 1873}], "paths_version": 1}