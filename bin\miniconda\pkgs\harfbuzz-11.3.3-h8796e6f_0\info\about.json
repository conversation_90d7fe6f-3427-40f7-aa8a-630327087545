{"channels": ["https://conda.anaconda.org/conda-forge"], "conda_build_version": "25.5.0", "conda_version": "25.5.1", "description": "HarfBuzz is a text shaping library. New Harbuzz targets various font\ntechnologies while Old HarfBuzz targets only OpenType fonts.\n", "dev_url": "https://github.com/harfbuzz/harfbuzz", "doc_url": "https://harfbuzz.github.io/", "env_vars": {"CIO_TEST": "<not set>"}, "extra": {"copy_test_source_files": true, "final": true, "flow_run_id": "azure_20250729.1.1", "recipe-maintainers": ["ocefpaf", "pkgw", "<PERSON><PERSON><PERSON><PERSON>"], "remote_url": "https://github.com/conda-forge/harfbuzz-feedstock", "sha": "f0e1db1ce0cc05bc365e04313ea4d9b710f3b153"}, "home": "https://harfbuzz.github.io/", "identifiers": [], "keywords": [], "license": "MIT", "license_file": "COPYING", "root_pkgs": ["anaconda-cli-base 0.5.2 pyhd8ed1ab_0", "anaconda-client 1.13.0 pyh29332c3_1", "annotated-types 0.7.0 pyhd8ed1ab_1", "archspec 0.2.5 pyhd8ed1ab_0", "attrs 25.3.0 pyh71513ae_0", "beautifulsoup4 4.13.4 pyha770c72_0", "boltons 25.0.0 pyhd8ed1ab_0", "brotli-python 1.1.0 py312h275cf98_3", "bzip2 1.0.8 h2466b09_7", "ca-certificates 2025.7.14 h4c7d964_0", "certifi 2025.7.14 pyhd8ed1ab_0", "cffi 1.17.1 py312h4389bb4_0", "chardet 5.2.0 pyhd8ed1ab_3", "charset-normalizer 3.4.2 pyhd8ed1ab_0", "click 8.2.1 pyh7428d3b_0", "colorama 0.4.6 pyhd8ed1ab_1", "conda 25.5.1 py312h2e8e312_0", "conda-build 25.5.0 py312h2e8e312_0", "conda-env 2.6.0 1", "conda-forge-ci-setup 4.21.2 py312h5eb20be_100", "conda-forge-metadata 0.11.0 pyhd8ed1ab_1", "conda-index 0.6.1 pyhd8ed1ab_0", "conda-libmamba-solver 25.4.0 pyhd8ed1ab_0", "conda-oci-mirror 0.2.3 pyhd8ed1ab_0", "conda-package-handling 2.4.0 pyh7900ff3_2", "conda-package-streaming 0.12.0 pyhd8ed1ab_0", "cpp-expected 1.1.0 hc790b64_1", "cpython 3.12.11 py312hd8ed1ab_0", "defusedxml 0.7.1 pyhd8ed1ab_0", "deprecated 1.2.18 pyhd8ed1ab_0", "distro 1.9.0 pyhd8ed1ab_1", "evalidate 2.0.5 pyhe01879c_0", "filelock 3.18.0 pyhd8ed1ab_0", "fmt 11.2.0 h1d4551f_0", "frozendict 2.4.6 py312h4389bb4_0", "h2 4.2.0 pyhd8ed1ab_0", "hpack 4.1.0 pyhd8ed1ab_0", "hyperframe 6.1.0 pyhd8ed1ab_0", "idna 3.10 pyhd8ed1ab_1", "jinja2 3.1.6 pyhd8ed1ab_0", "joblib 1.5.1 pyhd8ed1ab_0", "jsonpatch 1.33 pyhd8ed1ab_1", "jsonpointer 3.0.0 py312h2e8e312_1", "jsonschema 4.25.0 pyhe01879c_0", "jsonschema-specifications 2025.4.1 pyh29332c3_0", "jupyter_core 5.8.1 pyh5737063_0", "krb5 1.21.3 hdf4eb48_0", "lcms2 2.17 hbcf6048_0", "lerc 4.0.0 h6470a55_1", "libarchive 3.8.1 gpl_h1ca5a36_100", "libcurl 8.14.1 h88aaa65_0", "libdeflate 1.24 h76ddb4d_0", "libexpat 2.7.1 hac47afa_0", "libffi 3.4.6 h537db12_1", "libfreetype 2.13.3 h57928b3_1", "libfreetype6 2.13.3 h0b5ce68_1", "libgcc 15.1.0 h1383e82_3", "libgomp 15.1.0 h1383e82_3", "libiconv 1.18 h135ad9c_1", "libjpeg-turbo 3.1.0 h2466b09_0", "liblief 0.16.6 hac47afa_0", "liblzma 5.8.1 h2466b09_2", "libmamba 2.3.1 hd264f3a_1", "libmambapy 2.3.1 py312h19c6166_1", "libpng 1.6.50 h95bef1e_0", "libsolv 0.7.34 h8883371_0", "libsqlite 3.50.3 hf5d6505_1", "libssh2 1.11.1 h9aa295b_0", "libtiff 4.7.0 h05922d8_5", "libwebp-base 1.6.0 h4d5522a_0", "libwinpthread 12.0.0.r4.gg4f2fc60ca h57928b3_9", "libxcb 1.17.0 h0e4246c_0", "libxml2 2.13.8 h442d1da_0", "libzlib 1.3.1 h2466b09_2", "lz4-c 1.10.0 h2466b09_1", "lzo 2.10 hcfcfb64_1001", "m2-bash ********* hc364b38_6", "m2-brotli ******* hc364b38_6", "m2-ca-certificates 20241223.1 hc364b38_6", "m2-conda-epoch 20250515 0_x86_64", "m2-coreutils 8.32.5 hc364b38_6", "m2-curl ******* hc364b38_6", "m2-db ******** hc364b38_6", "m2-file 5.46.2 hc364b38_6", "m2-filesystem 2025.02.23.1 hc364b38_8", "m2-findutils ******* hc364b38_6", "m2-gcc-libs ******** hc364b38_6", "m2-gdbm 1.25.1 hc364b38_6", "m2-git ******** hc364b38_6", "m2-gmp ******* hc364b38_6", "m2-gzip 1.14.1 hc364b38_6", "m2-heimdal ******* hc364b38_6", "m2-heimdal-libs ******* hc364b38_6", "m2-info 7.2.1 hc364b38_6", "m2-less 668.1 hc364b38_6", "m2-libbz2 ******* hc364b38_6", "m2-libcbor ******** hc364b38_6", "m2-libcurl ******* hc364b38_6", "m2-libdb ******** hc364b38_6", "m2-libedit 20240808_3.1.1 hc364b38_6", "m2-libexpat ******* hc364b38_6", "m2-libffi ******* hc364b38_6", "m2-libfido2 ******** hc364b38_6", "m2-libgdbm 1.25.1 hc364b38_6", "m2-libiconv 1.18.1 hc364b38_6", "m2-libidn2 ******* hc364b38_6", "m2-libintl ******** hc364b38_6", "m2-liblzma ******* hc364b38_6", "m2-libnghttp2 ******** hc364b38_6", "m2-libopenssl ******* hc364b38_6", "m2-libp11-kit ******** hc364b38_6", "m2-libpcre2_8 10.45.1 hc364b38_6", "m2-libpsl ******** hc364b38_6", "m2-libreadline ********* hc364b38_6", "m2-libsqlite ******** hc364b38_6", "m2-libssh2 ******** hc364b38_6", "m2-libtasn1 ******** hc364b38_6", "m2-libunistring 1.3.1 hc364b38_6", "m2-libxcrypt ******** hc364b38_6", "m2-libzstd 1.5.7.1 hc364b38_6", "m2-msys2-runtime 3.6.1.4 hc364b38_6", "m2-nano 8.4.1 hc364b38_6", "m2-ncurses 6.5.20240831.2 hc364b38_6", "m2-openssh 9.9p2.1 hc364b38_6", "m2-openssl ******* hc364b38_6", "m2-p11-kit ******** hc364b38_6", "m2-patch 2.7.6.3 hc364b38_6", "m2-perl 5.38.4.2 hc364b38_6", "m2-perl-authen-sasl 2.1800.1 hc364b38_6", "m2-perl-clone 0.47.1 hc364b38_6", "m2-perl-convert-binhex 1.125.2 hc364b38_6", "m2-perl-encode-locale 1.05.2 hc364b38_6", "m2-perl-error 0.17030.1 hc364b38_6", "m2-perl-file-listing 6.16.1 hc364b38_6", "m2-perl-html-parser 3.83.1 hc364b38_6", "m2-perl-html-tagset 3.24.1 hc364b38_6", "m2-perl-http-cookiejar 0.014.1 hc364b38_6", "m2-perl-http-cookies 6.11.1 hc364b38_6", "m2-perl-http-daemon 6.16.1 hc364b38_6", "m2-perl-http-date 6.06.1 hc364b38_6", "m2-perl-http-message 7.00.1 hc364b38_6", "m2-perl-http-negotiate 6.01.3 hc364b38_6", "m2-perl-io-html 1.004.2 hc364b38_6", "m2-perl-io-socket-ip 0.41.2 hc364b38_6", "m2-perl-io-socket-ssl 2.089.1 hc364b38_6", "m2-perl-io-stringy 2.113.2 hc364b38_6", "m2-perl-libwww 6.78.1 hc364b38_6", "m2-perl-lwp-mediatypes 6.04.2 hc364b38_6", "m2-perl-mailtools 2.22.1 hc364b38_6", "m2-perl-mime-tools 5.515.1 hc364b38_6", "m2-perl-net-http 6.23.1 hc364b38_6", "m2-perl-net-smtp-ssl 1.04.2 hc364b38_6", "m2-perl-net-ssleay 1.94.2 hc364b38_6", "m2-perl-termreadkey 2.38.6 hc364b38_6", "m2-perl-timedate 2.33.2 hc364b38_6", "m2-perl-try-tiny 0.32.1 hc364b38_6", "m2-perl-uri 5.31.1 hc364b38_6", "m2-perl-www-robotrules 6.02.3 hc364b38_6", "m2-sed 4.9.1 hc364b38_6", "m2-zlib ******* hc364b38_6", "mamba 2.3.1 h828c8c6_1", "markdown-it-py 3.0.0 pyhd8ed1ab_1", "markupsafe 3.0.2 py312h31fea79_1", "mbedtls ******* he0c23c2_0", "mdurl 0.1.2 pyhd8ed1ab_1", "menuinst 2.3.1 py312hbb81ca0_0", "msgpack-python 1.1.1 py312hd5eb7cc_0", "nbformat 5.10.4 pyhd8ed1ab_1", "n<PERSON><PERSON>_<PERSON>son 3.11.3 he0c23c2_1", "openjpeg 2.5.3 h4d64b90_0", "openssl 3.5.1 h725018a_0", "oras-py 0.1.14 pyhd8ed1ab_0", "packaging 25.0 pyh29332c3_1", "pillow 11.3.0 py312hfb502af_0", "pip 25.1.1 pyh8b19718_0", "pkginfo ******** pyhd8ed1ab_0", "platformdirs 4.3.8 pyhe01879c_0", "pluggy 1.6.0 pyhd8ed1ab_0", "psutil 7.0.0 py312h4389bb4_0", "pthread-stubs 0.4 h0e40799_1002", "py-lief 0.16.6 py312hbb81ca0_0", "pybind11-abi 4 hd8ed1ab_3", "pycosat 0.6.6 py312h4389bb4_2", "pycparser 2.22 pyh29332c3_1", "pydantic 2.11.7 pyh3cfb1c2_0", "pydantic-core 2.33.2 py312h8422cdd_0", "pydantic-settings 2.10.1 pyh3cfb1c2_0", "pygments 2.19.2 pyhd8ed1ab_0", "pysocks 1.7.1 pyh09c184e_7", "python 3.12.11 h3f84c4b_0_cpython", "python-dateutil 2.9.0.post0 pyhe01879c_2", "python-dotenv 1.1.1 pyhe01879c_0", "python-fastjsonschema 2.21.1 pyhd8ed1ab_0", "python-libarchive-c 5.3 pyhe01879c_0", "python_abi 3.12 8_cp312", "pytz 2025.2 pyhd8ed1ab_0", "pywin32 311 py312h829343e_0", "pyyaml 6.0.2 py312h31fea79_2", "rattler-build 0.44.0 h18a1a76_0", "rattler-build-conda-compat 1.4.3 pyhd8ed1ab_0", "readchar 4.2.1 pyhe01879c_0", "referencing 0.36.2 pyh29332c3_0", "reproc 14.2.5.post0 h2466b09_0", "reproc-cpp 14.2.5.post0 he0c23c2_0", "requests 2.32.4 pyhd8ed1ab_0", "requests-toolbelt 1.0.0 pyhd8ed1ab_1", "rich 14.1.0 pyhe01879c_0", "ripgrep 14.1.1 ha073cba_1", "rpds-py 0.26.0 py312hdabe01f_0", "ruamel.yaml 0.18.14 py312h4389bb4_0", "ruamel.yaml.clib 0.2.8 py312h4389bb4_1", "setuptools 80.9.0 pyhff2d567_0", "shellingham 1.5.4 pyhd8ed1ab_1", "shyaml 0.6.2 pyhd3deb0d_0", "simd<PERSON>son 3.13.0 hc790b64_0", "six 1.17.0 pyhe01879c_1", "soupsieve 2.7 pyhd8ed1ab_0", "tk 8.6.13 h2c6b04d_2", "tomli 2.2.1 pyhd8ed1ab_1", "tqdm 4.67.1 pyhd8ed1ab_1", "traitlets 5.14.3 pyhd8ed1ab_1", "truststore 0.10.1 pyh29332c3_0", "typer 0.16.0 pyh167b9f4_0", "typer-slim 0.16.0 pyhe01879c_0", "typer-slim-standard 0.16.0 hf964461_0", "typing-extensions 4.14.1 h4440ef1_0", "typing-inspection 0.4.1 pyhd8ed1ab_0", "typing_extensions 4.14.1 pyhe01879c_0", "tzdata 2025b h78e105d_0", "ucrt 10.0.22621.0 h57928b3_1", "urllib3 2.5.0 pyhd8ed1ab_0", "vc 14.3 h41ae7f8_31", "vc14_runtime 14.44.35208 h818238b_31", "vcomp14 14.44.35208 h818238b_31", "wheel 0.45.1 pyhd8ed1ab_1", "win_inet_pton 1.1.0 pyh7428d3b_8", "wrapt 1.17.2 py312h4389bb4_0", "xorg-libxau 1.0.12 h0e40799_0", "xorg-libxdmcp 1.1.5 h0e40799_0", "yaml 0.2.5 h6a83c73_3", "yaml-cpp 0.8.0 he0c23c2_0", "zstandard 0.23.0 py312h4389bb4_2", "zstd 1.5.7 hbeecb71_2", "_openmp_mutex 4.5 2_gnu"], "summary": "An OpenType text shaping engine.", "tags": []}