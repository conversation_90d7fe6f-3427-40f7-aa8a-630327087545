VERBOSE_AT: V=1
VERBOSE_CM: VERBOSE=1
apr: 1.6.3
blas_impl: mkl
boost: '1.82'
boost_cpp: '1.82'
bzip2: '1.0'
c_compiler: vs2017
cairo: '1.16'
channel_targets: defaults
clang_variant: clang
cpu_optimization_target: nocona
cran_mirror: https://mran.microsoft.com/snapshot/2018-01-01
cross_compiler_target_platform: win-64
cxx_compiler: vs2017
cyrus_sasl: 2.1.28
dbus: '1'
expat: '2'
extend_keys:
- ignore_build_only_deps
- pin_run_as_build
- ignore_version
- extend_keys
fontconfig: '2.14'
fortran_compiler: intel-fortran
fortran_compiler_version: 2022.1.0
freetype: '2.10'
g2clib: '1.6'
geos: 3.8.0
giflib: '5'
glib: '2'
gmp: '6.1'
gnu: 2.12.2
gst_plugins_base: '1.18'
gstreamer: '1.18'
harfbuzz: 4.3.0
hdf4: '4.2'
hdf5: 1.12.1
hdfeos2: '2.20'
hdfeos5: '5.1'
icu: '73'
ignore_build_only_deps:
- numpy
- python
jpeg: '9'
libcurl: 8.1.1
libdap4: '3.19'
libffi: '3.4'
libgd: 2.3.3
libgdal: 3.6.2
libgsasl: '1.10'
libkml: '1.3'
libnetcdf: '4.8'
libpng: '1.6'
libprotobuf: 3.20.3
libtiff: '4.2'
libwebp: 1.3.2
libxml2: '2.10'
libxslt: '1.1'
llvm_variant: llvm
lua: '5'
lzo: '2'
m2w64_c_compiler: m2w64-toolchain
m2w64_cxx_compiler: m2w64-toolchain
m2w64_fortran_compiler: m2w64-toolchain
mkl: 2023.*
mpfr: '4'
numpy: '1.21'
openblas: 0.3.21
openjpeg: '2.3'
openssl: '3.0'
perl: '5.26'
pin_run_as_build:
  python:
    max_pin: x.x
    min_pin: x.x
  r-base:
    max_pin: x.x
    min_pin: x.x
  libboost:
    max_pin: x.x.x
pixman: '0.40'
proj: 9.3.1
proj4: 5.2.0
python: '3.9'
python_impl: cpython
python_implementation: cpython
r_base: '3.4'
r_implementation: mro-base
r_version: 3.5.0
readline: '8.0'
rust_compiler: rust
rust_compiler_version: 1.71.1
rust_gnu_compiler: rust-gnu
rust_gnu_compiler_version: 1.71.1
serf: 1.3.9
sqlite: '3'
target_platform: win-64
tk: '8.6'
vc: '14'
xz: '5'
zip_keys:
- - python
  - numpy
zlib: '1.2'
zstd: 1.5.2
