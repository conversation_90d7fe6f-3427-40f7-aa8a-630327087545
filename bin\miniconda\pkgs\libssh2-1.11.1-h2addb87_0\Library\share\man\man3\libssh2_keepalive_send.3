.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_keepalive_send 3 "13 Apr 2011" "libssh2" "libssh2"
.SH NAME
libssh2_keepalive_send - short function description
.SH SYNOPSIS
.nf
#include <libssh2.h>

int
libssh2_keepalive_send(LIBSSH2_SESSION *session,
                       int *seconds_to_next);
.fi
.SH DESCRIPTION
Send a keepalive message if needed. \fBseconds_to_next\fP indicates how many
seconds you can sleep after this call before you need to call it again.
.SH RETURN VALUE
Returns 0 on success, or LIBSSH2_ERROR_SOCKET_SEND on I/O errors.
.SH AVAILABILITY
Added in libssh2 1.2.5
.SH SEE ALSO
.BR libssh2_keepalive_config(3)
