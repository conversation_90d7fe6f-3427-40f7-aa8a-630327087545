.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_session_block_directions 3 "1 Oct 2008" "libssh2" "libssh2"
.SH NAME
libssh2_session_block_directions - get directions to wait for
.SH SYNOPSIS
.nf
#include <libssh2.h>

int
libssh2_session_block_directions(LIBSSH2_SESSION *session);
.fi
.SH DESCRIPTION
\fIsession\fP - Session instance as returned by \fBlibssh2_session_init_ex(3)\fP

When any of libssh2 functions return \fBLIBSSH2_ERROR_EAGAIN\fP an application
should wait for the socket to have data available for reading or
writing. Depending on the return value of
\fIlibssh2_session_block_directions(3)\fP an application should wait for read,
write or both.
.SH RETURN VALUE
Returns the set of directions as a binary mask. Can be a combination of:

LIBSSH2_SESSION_BLOCK_INBOUND: Inbound direction blocked.

LIBSSH2_SESSION_BLOCK_OUTBOUND: Outbound direction blocked.

Application should wait for data to be available for socket prior to calling a
libssh2 function again. If \fBLIBSSH2_SESSION_BLOCK_INBOUND\fP is set select
should contain the session socket in readfds set. Correspondingly in case of
\fBLIBSSH2_SESSION_BLOCK_OUTBOUND\fP writefds set should contain the socket.
.SH AVAILABILITY
Added in 1.0
