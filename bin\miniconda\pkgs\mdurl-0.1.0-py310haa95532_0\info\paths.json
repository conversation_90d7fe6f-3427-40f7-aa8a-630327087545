{"paths": [{"_path": "Lib/site-packages/mdurl-0.1.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/mdurl-0.1.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "7c605df6e28667a9603118e98274f64a49ce3eed0d26fccce9534a345e0ef955", "size_in_bytes": 2338}, {"_path": "Lib/site-packages/mdurl-0.1.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "3856e00b30366fcaedaaa557f3850bcead1896530eb105bcc8aa3e4cc71efc58", "size_in_bytes": 4718}, {"_path": "Lib/site-packages/mdurl-0.1.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "738f7f57b5e8dd77bf0cc50519b2718c59016796757fad7de22013eef32dc514", "size_in_bytes": 1332}, {"_path": "Lib/site-packages/mdurl-0.1.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/mdurl-0.1.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "8cf311fc3ce47385f889c42d9b3f35967358fe402c7e883baf2eeaa11bd82d7c", "size_in_bytes": 81}, {"_path": "Lib/site-packages/mdurl-0.1.0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "0cb1214276d2473dc6667eabe05ba2411cad49413096606946a474a746e19dc5", "size_in_bytes": 104}, {"_path": "Lib/site-packages/mdurl/__init__.py", "path_type": "hardlink", "sha256": "59ba175b63402d5475ac8ab3d0fc1aab0da1308ada84121265793b822a36e510", "size_in_bytes": 547}, {"_path": "Lib/site-packages/mdurl/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "b784e7494d14440e8c481905d8e2275b5f9fcf4b57ca1de6c6f15a1dd2e53ac6", "size_in_bytes": 567}, {"_path": "Lib/site-packages/mdurl/__pycache__/_decode.cpython-310.pyc", "path_type": "hardlink", "sha256": "8b0ada3b697af52fa344362799127982129c2e4369045072be14e999a5c06a36", "size_in_bytes": 2279}, {"_path": "Lib/site-packages/mdurl/__pycache__/_encode.cpython-310.pyc", "path_type": "hardlink", "sha256": "7a2d65144fbd6421575e58f97df5a48398d09450c8c32ef4007eb2a78bfa1a29", "size_in_bytes": 1797}, {"_path": "Lib/site-packages/mdurl/__pycache__/_format.cpython-310.pyc", "path_type": "hardlink", "sha256": "528bdb0922367300c7c1792409ef316762764a65c9d669ff33faceee463974ad", "size_in_bytes": 637}, {"_path": "Lib/site-packages/mdurl/__pycache__/_parse.cpython-310.pyc", "path_type": "hardlink", "sha256": "b3ff3044272145a4f2a0a6bdc727f58377038741a85d1339e61adf6133266445", "size_in_bytes": 3966}, {"_path": "Lib/site-packages/mdurl/__pycache__/_url.cpython-310.pyc", "path_type": "hardlink", "sha256": "66dd954da0229b07be2682b57031ad505f7cb86a4a4e3941385a37b29a6ff27e", "size_in_bytes": 544}, {"_path": "Lib/site-packages/mdurl/_decode.py", "path_type": "hardlink", "sha256": "2b3cf417cb0cf996cb8f48a57f9361de6fee5c8dd651b998f03a38eb8a5b7469", "size_in_bytes": 3291}, {"_path": "Lib/site-packages/mdurl/_encode.py", "path_type": "hardlink", "sha256": "e23ae0fa61f4956a7fbc7d1503b03cc4003abc6be3d1325473e928e59de293c7", "size_in_bytes": 2569}, {"_path": "Lib/site-packages/mdurl/_format.py", "path_type": "hardlink", "sha256": "01031147fef2acbeb9c30f1ce0c2ae01da39d2f969c409f26dd7febe56d90b5b", "size_in_bytes": 592}, {"_path": "Lib/site-packages/mdurl/_parse.py", "path_type": "hardlink", "sha256": "20266f50b511de72598e105fe9a1c6340a1f925689c8ccdae6b33a6e3fcab079", "size_in_bytes": 11383}, {"_path": "Lib/site-packages/mdurl/_url.py", "path_type": "hardlink", "sha256": "177f995e740b4c156afd3201e0efd87290647ae35f023d69b68775624f63ef4c", "size_in_bytes": 279}, {"_path": "Lib/site-packages/mdurl/py.typed", "path_type": "hardlink", "sha256": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "size_in_bytes": 26}], "paths_version": 1}