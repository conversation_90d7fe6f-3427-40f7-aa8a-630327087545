.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_channel_request_auth_agent 3 "1 Jun 2007" "libssh2 0.15" "libssh2"
.SH NAME
libssh2_channel_request_auth_agent - request agent forwarding for a session
.SH SYNOPSIS
.nf
#include <libssh2.h>

int
libssh2_channel_request_auth_agent(LIBSSH2_CHANNEL *channel);
.fi
.SH DESCRIPTION
Request that agent forwarding be enabled for this SSH session. This sends the
request over this specific channel, which causes the agent listener to be
started on the remote side upon success. This agent listener will then run
for the duration of the SSH session.

\fIchannel\fP - Previously opened channel instance such as returned by
.BR libssh2_channel_open_ex(3)
.SH RETURN VALUE
Return 0 on success or negative on failure. It returns
LIBSSH2_ERROR_EAGAIN when it would otherwise block. While
LIBSSH2_ERROR_EAGAIN is a negative number, it is not really a failure per se.
