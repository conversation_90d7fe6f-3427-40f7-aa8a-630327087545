# This file created by conda-build 3.24.0
# meta.yaml template originally from:
# C:\b\abs_a5bfngz6fu\clone\recipe, last modified Wed May 17 00:31:42 2023
# ------------------------------------------------

package:
  name: markdown-it-py
  version: 2.2.0
source:
  sha256: 7c9a5e412688bc771c67432cbfebcdd686c93ce6484913dccf06cb5a0bea35a1
  url: https://pypi.io/packages/source/m/markdown-it-py/markdown-it-py-2.2.0.tar.gz
build:
  entry_points:
    - markdown-it = markdown_it.cli.parse:main
  number: '1'
  script: C:\\b\\abs_a5bfngz6fu\\croot\\markdown-it-py_1684279915556\\_h_env\\python.exe
    -m pip install . --no-deps --no-build-isolation -vv
  string: py310haa95532_1
requirements:
  host:
    - bzip2 1.0.8 he774522_0
    - ca-certificates 2023.01.10 haa95532_0
    - flit-core 3.8.0 py310haa95532_0
    - libffi 3.4.4 hd77b12b_0
    - openssl 1.1.1t h2bbff1b_0
    - pip 23.0.1 py310haa95532_0
    - python 3.10.11 h966fe2a_2
    - setuptools 66.0.0 py310haa95532_0
    - sqlite 3.41.2 h2bbff1b_0
    - tk 8.6.12 h2bbff1b_0
    - tzdata 2023c h04d1e81_0
    - vc 14.2 h21ff451_1
    - vs2015_runtime 14.27.29016 h5e58377_2
    - wheel 0.38.4 py310haa95532_0
    - xz 5.4.2 h8cc25b3_0
    - zlib 1.2.13 h8cc25b3_0
  run:
    - mdurl >=0.1,<1
    - python >=3.10,<3.11.0a0
test:
  commands:
    - markdown-it --help
    - pip check
  imports:
    - markdown_it
    - markdown_it.cli
    - markdown_it.common
    - markdown_it.helpers
    - markdown_it.presets
    - markdown_it.rules_block
    - markdown_it.rules_core
    - markdown_it.rules_inline
  requires:
    - pip
about:
  description: 'Python port of markdown-it. Markdown parsing, done right!

    '
  dev_url: https://github.com/ExecutableBookProject/markdown-it-py
  doc_url: https://github.com/ExecutableBookProject/markdown-it-py/blob/master/README.md
  home: https://github.com/ExecutableBookProject/markdown-it-py
  license: MIT
  license_family: MIT
  license_file: LICENSE
  summary: Python port of markdown-it. Markdown parsing, done right!
extra:
  copy_test_source_files: true
  final: true
  flow_run_id: a5518e75-6498-497e-8f7f-7ef7ddd2bb57
  recipe-maintainers:
    - choldgraf
    - dopplershift
  remote_url: **************:AnacondaRecipes/markdown-it-py-feedstock.git
  sha: 72cbec664b2558efd026a48af6644e04bb5a5ab5
