.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_channel_direct_streamlocal_ex 3 "10 Apr 2023" "libssh2 1.11.0" "libssh2"
.SH NAME
libssh2_channel_direct_streamlocal_ex - Tunnel a UNIX socket connection through an SSH session
.SH SYNOPSIS
.nf
#include <libssh2.h>

LIBSSH2_CHANNEL *
libssh2_channel_direct_streamlocal_ex(LIBSSH2_SESSION *session,
                                      const char *socket_path,
                                      const char *shost, int sport);
.fi
.SH DESCRIPTION
\fIsession\fP - Session instance as returned by
.BR libssh2_session_init_ex(3)

\fIsocket_path\fP - UNIX socket to connect to using the SSH host as a proxy.

\fIshost\fP - Host to tell the SSH server the connection originated on.

\fIsport\fP - Port to tell the SSH server the connection originated from.

Tunnel a UNIX socket connection through the SSH transport via the remote host to
a third party. Communication from the client to the SSH server remains
encrypted, communication from the server to the 3rd party host travels
in cleartext.
.SH RETURN VALUE
Pointer to a newly allocated LIBSSH2_CHANNEL instance, or NULL on errors.
.SH ERRORS
\fILIBSSH2_ERROR_ALLOC\fP - An internal memory allocation call failed.
.SH SEE ALSO
.BR libssh2_session_init_ex(3)
