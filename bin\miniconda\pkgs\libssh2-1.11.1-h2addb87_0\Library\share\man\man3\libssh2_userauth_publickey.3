.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_userauth_publickey 3 "1 Jun 2007" "libssh2 0.15" "libssh2"
.SH NAME
libssh2_userauth_publickey - authenticate using a callback function
.SH SYNOPSIS
.nf
#include <libssh2.h>

int
libssh2_userauth_publickey(LIBSSH2_SESSION *session,
                           const char *user,
                           const unsigned char *pubkeydata,
                           size_t pubkeydata_len,
                           sign_callback,
                           void **abstract);
.fi
.SH DESCRIPTION
Authenticate with the \fIsign_callback\fP callback that matches the prototype
below
.SH CALLBACK
.nf
int name(LIBSSH2_SESSION *session, unsigned char **sig, size_t *sig_len,
         const unsigned char *data, size_t data_len, void **abstract);
.fi

This function gets called...
.SH RETURN VALUE
Return 0 on success or negative on failure.
.SH SEE ALSO
.BR libssh2_userauth_publickey_fromfile_ex(3)
