.\" Copyright (C) <PERSON><PERSON>
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_agent_list_identities 3 "23 Dec 2009" "libssh2" "libssh2"
.SH NAME
libssh2_agent_list_identities - request an ssh-agent to list of public keys.
.SH SYNOPSIS
.nf
#include <libssh2.h>

int
libssh2_agent_list_identities(LIBSSH2_AGENT *agent);
.fi
.SH DESCRIPTION
Request an ssh-agent to list of public keys, and stores them in the
internal collection of the handle. Call
\fIlibssh2_agent_get_identity(3)\fP to get a public key off the
collection.

.SH RETURN VALUE
Returns 0 if succeeded, or a negative value for error.
.SH AVAILABILITY
Added in libssh2 1.2
.SH SEE ALSO
.BR libssh2_agent_connect(3)
.BR libssh2_agent_get_identity(3)
