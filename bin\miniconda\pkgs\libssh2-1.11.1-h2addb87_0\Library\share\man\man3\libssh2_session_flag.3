.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_session_flag 3 "1 Jun 2007" "libssh2 0.15" "libssh2"
.SH NAME
libssh2_session_flag - TODO
.SH SYNOPSIS
.nf
#include <libssh2.h>

int
libssh2_session_flag(LIBSSH2_SESSION *session, int flag, int value);
.fi
.SH DESCRIPTION
Set options for the created session. \fIflag\fP is the option to set, while
\fIvalue\fP is typically set to 1 or 0 to enable or disable the option.
.SH FLAGS
.IP LIBSSH2_FLAG_SIGPIPE
If set, libssh2 will not attempt to block SIGPIPEs but will let them trigger
from the underlying socket layer.
.IP LIBSSH2_FLAG_COMPRESS
If set - before the connection negotiation is performed - libssh2 will try to
negotiate compression enabling for this connection. By default libssh2 will
not attempt to use compression.
.SH RETURN VALUE
Returns regular libssh2 error code.
.SH AVAILABILITY
This function has existed since the age of dawn. LIBSSH2_FLAG_COMPRESS was
added in version 1.2.8.
.SH SEE ALSO
