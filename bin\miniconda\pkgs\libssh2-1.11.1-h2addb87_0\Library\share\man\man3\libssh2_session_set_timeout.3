.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_session_set_timeout 3 "4 May 2011" "libssh2" "libssh2"
.SH NAME
libssh2_session_set_timeout - set timeout for blocking functions
.SH SYNOPSIS
.nf
#include <libssh2.h>

void
libssh2_session_set_timeout(LIBSSH2_SESSION *session, long timeout);
.fi
.SH DESCRIPTION
Set the \fBtimeout\fP in milliseconds for how long a blocking the libssh2
function calls may wait until they consider the situation an error and return
LIBSSH2_ERROR_TIMEOUT.

By default or if you set the timeout to zero, libssh2 has no timeout for
blocking functions.
.SH RETURN VALUE
Nothing
.SH AVAILABILITY
Added in 1.2.9
.SH SEE ALSO
.BR libssh2_session_get_timeout(3)
