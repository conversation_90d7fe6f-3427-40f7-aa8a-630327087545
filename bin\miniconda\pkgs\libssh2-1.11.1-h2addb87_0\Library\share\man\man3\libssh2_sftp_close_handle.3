.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_sftp_close_handle 3 "1 Jun 2007" "libssh2 0.15" "libssh2"
.SH NAME
libssh2_sftp_close_handle - close filehandle
.SH SYNOPSIS
.nf
#include <libssh2.h>
#include <libssh2_sftp.h>

int
libssh2_sftp_close_handle(LIBSSH2_SFTP_HANDLE *handle);

int
libssh2_sftp_close(LIBSSH2_SFTP_HANDLE *handle);

int
libssh2_sftp_closedir(LIBSSH2_SFTP_HANDLE *handle);
.fi
.SH DESCRIPTION
\fIhandle\fP - SFTP File Handle as returned by \fBlibssh2_sftp_open_ex(3)\fP
or \fBlibssh2_sftp_opendir(3)\fP (which is a macro).

Close an active LIBSSH2_SFTP_HANDLE. Because files and directories share the
same underlying storage mechanism these methods may be used
interchangeably. \fBlibssh2_sftp_close(3)\fP and \fBlibssh2_sftp_closedir(3)\fP
are macros for \fBlibssh2_sftp_close_handle(3)\fP.
.SH RETURN VALUE
Return 0 on success or negative on failure. It returns
LIBSSH2_ERROR_EAGAIN when it would otherwise block. While
LIBSSH2_ERROR_EAGAIN is a negative number, it is not really a failure per se.
.SH ERRORS
\fILIBSSH2_ERROR_ALLOC\fP - An internal memory allocation call failed.

\fILIBSSH2_ERROR_SOCKET_SEND\fP - Unable to send data on socket.

\fILIBSSH2_ERROR_SOCKET_TIMEOUT\fP -

\fILIBSSH2_ERROR_SFTP_PROTOCOL\fP - An invalid SFTP protocol response was
received on the socket, or an SFTP operation caused an errorcode to
be returned by the server.
.SH SEE ALSO
.BR libssh2_sftp_open_ex(3)
