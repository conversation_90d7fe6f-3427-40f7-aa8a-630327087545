.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_channel_read_ex 3 "1 Jun 2007" "libssh2 0.15" "libssh2"
.SH NAME
libssh2_channel_read_ex - read data from a channel stream
.SH SYNOPSIS
.nf
#include <libssh2.h>

ssize_t
libssh2_channel_read_ex(LIBSSH2_CHANNEL *channel, int stream_id,
                        char *buf, size_t buflen);

ssize_t
libssh2_channel_read(LIBSSH2_CHANNEL *channel,
                     char *buf, size_t buflen);

ssize_t
libssh2_channel_read_stderr(LIBSSH2_CHANNEL *channel,
                            char *buf, size_t buflen);
.fi
.SH DESCRIPTION
Attempt to read data from an active channel stream. All channel streams have
one standard I/O substream (stream_id == 0), and may have up to 2^32 extended
data streams as identified by the selected \fIstream_id\fP. The SSH2 protocol
currently defines a stream ID of 1 to be the stderr substream.

\fIchannel\fP - active channel stream to read from.

\fIstream_id\fP - substream ID number (e.g. 0 or SSH_EXTENDED_DATA_STDERR)

\fIbuf\fP - pointer to storage buffer to read data into

\fIbuflen\fP - size of the buf storage

\fIlibssh2_channel_read(3)\fP and \fIlibssh2_channel_read_stderr(3)\fP are
macros.
.SH RETURN VALUE
Actual number of bytes read or negative on failure. It returns
LIBSSH2_ERROR_EAGAIN when it would otherwise block. While
LIBSSH2_ERROR_EAGAIN is a negative number, it is not really a failure per se.

Note that a return value of zero (0) can in fact be a legitimate value and
only signals that no payload data was read. It is not an error.
.SH ERRORS
\fILIBSSH2_ERROR_SOCKET_SEND\fP - Unable to send data on socket.

\fILIBSSH2_ERROR_CHANNEL_CLOSED\fP - The channel has been closed.
.SH SEE ALSO
.BR libssh2_poll_channel_read(3)
