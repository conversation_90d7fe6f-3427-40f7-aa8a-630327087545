# This file created by conda-build 3.24.0
# meta.yaml template originally from:
# C:\b\abs_b12svzu_88\clone\recipe, last modified Wed Mar 29 10:50:40 2023
# ------------------------------------------------

package:
  name: nlohmann_json
  version: 3.11.2
source:
  sha256: d69f9deb6a75e2580465c6c4c5111b89c4dc2fa94e3a85fcd2ffcd9a143d9273
  url: https://github.com/nlohmann/json/archive/v3.11.2.tar.gz
build:
  ignore_run_exports:
    - libcxx
    - libgcc-ng
    - libstdcxx-ng
    - vc
    - vs2015_runtime
  number: '0'
  string: h6c2663c_0
requirements:
  build:
    - bzip2 1.0.8 he774522_0
    - ca-certificates 2023.01.10 haa95532_0
    - cmake 3.22.1 h9ad04ae_0
    - libffi 3.4.2 hd77b12b_6
    - libuv 1.44.2 h2bbff1b_0
    - lz4-c 1.9.4 h2bbff1b_0
    - ninja 1.10.2 haa95532_5
    - ninja-base 1.10.2 h6d14046_5
    - openssl 1.1.1t h2bbff1b_0
    - python 3.11.2 h966fe2a_0
    - sqlite 3.41.1 h2bbff1b_0
    - tk 8.6.12 h2bbff1b_0
    - tzdata 2022g h04d1e81_0
    - vc 14.2 h21ff451_1
    - vs2015_runtime 14.27.29016 h5e58377_2
    - vs2017_win-64 19.16.27032.1 hb4161e2_3
    - vswhere 2.8.4 haa95532_0
    - xz 5.2.10 h8cc25b3_1
    - zlib 1.2.13 h8cc25b3_0
    - zstd 1.5.4 hd43e919_0
  host: []
  run: []
test:
  commands:
    - if not exist %LIBRARY_PREFIX%\include\nlohmann\json.hpp (exit 1)
    - if not exist %LIBRARY_PREFIX%\include\nlohmann\json_fwd.hpp (exit 1)
    - if not exist %LIBRARY_PREFIX%\share\cmake\nlohmann_json\nlohmann_jsonConfig.cmake
      (exit 1)
    - if not exist %LIBRARY_PREFIX%\share\cmake\nlohmann_json\nlohmann_jsonConfigVersion.cmake
      (exit 1)
    - if not exist %LIBRARY_PREFIX%\share\cmake\nlohmann_json\nlohmann_jsonTargets.cmake
      (exit 1)
about:
  description: JSON for Modern C++ is a C++11 JSON parser.
  dev_url: https://github.com/nlohmann/json
  doc_url: https://nlohmann.github.io/json/
  home: https://github.com/nlohmann/json
  license: MIT
  license_family: MIT
  license_file: LICENSE.MIT
  summary: JSON for Modern C++
extra:
  copy_test_source_files: true
  final: true
  flow_run_id: b1e25400-e016-47d0-bd98-f200a58d9d59
  recipe-maintainers:
    - JohanMabille
    - SylvainCorlay
    - constantinpape
    - wolfv
  remote_url: **************:AnacondaRecipes/nlohmann_json-feedstock.git
  sha: d5caab31d1166826079c5a35fe4802b173f56d57
