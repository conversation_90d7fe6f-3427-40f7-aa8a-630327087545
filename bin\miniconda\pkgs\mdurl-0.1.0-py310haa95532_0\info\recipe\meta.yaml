# This file created by conda-build 3.21.9
# meta.yaml template originally from:
# C:\Windows\TEMP\abs_3197pzpjbi\clone\recipe, last modified Fri Aug  5 16:13:45 2022
# ------------------------------------------------

package:
  name: mdurl
  version: 0.1.0
source:
  sha256: 94873a969008ee48880fb21bad7de0349fef529f3be178969af5817239e9b990
  url: https://pypi.io/packages/source/m/mdurl/mdurl-0.1.0.tar.gz
build:
  number: '0'
  script: C:\Windows\TEMP\abs_3197pzpjbi\croots\recipe\mdurl_1659716032440\_h_env\python.exe
    -m pip install . -vv
  string: py310haa95532_0
requirements:
  host:
    - bzip2 1.0.8 he774522_0
    - ca-certificates 2022.07.19 haa95532_0
    - certifi 2022.6.15 py310haa95532_0
    - flit-core 3.6.0 pyhd3eb1b0_0
    - libffi 3.4.2 hd77b12b_4
    - openssl 1.1.1q h2bbff1b_0
    - pip 22.1.2 py310haa95532_0
    - python 3.10.4 hbb2ffb3_0
    - setuptools 61.2.0 py310haa95532_0
    - sqlite 3.39.0 h2bbff1b_0
    - tk 8.6.12 h2bbff1b_0
    - tzdata 2022a hda174b7_0
    - vc 14.2 h21ff451_1
    - vs2015_runtime 14.27.29016 h5e58377_2
    - wheel 0.37.1 pyhd3eb1b0_0
    - wincertstore 0.2 py310haa95532_2
    - xz 5.2.5 h8cc25b3_1
    - zlib 1.2.12 h8cc25b3_2
  run:
    - python >=3.10,<3.11.0a0
test:
  imports:
    - mdurl
about:
  home: https://github.com/executablebooks/mdurl
  license: MIT
  license_family: MIT
  license_file: LICENSE
  summary: URL utilities for markdown-it-py parser.
extra:
  copy_test_source_files: true
  final: true
  flow_run_id: 31e74ecb-fb68-410f-9cf3-0b1553d18306
  recipe-maintainers:
    - chrisjsewell
  remote_url: **************:AnacondaRecipes/mdurl-feedstock.git
  sha: f6f2c5333f81f9e6e80de5c3c7e46b4f565229b9
