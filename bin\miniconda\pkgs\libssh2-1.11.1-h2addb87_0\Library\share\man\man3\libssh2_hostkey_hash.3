.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_hostkey_hash 3 "1 Jun 2007" "libssh2 0.15" "libssh2"
.SH NAME
libssh2_hostkey_hash - return a hash of the remote host's key
.SH SYNOPSIS
.nf
#include <libssh2.h>

const char *
libssh2_hostkey_hash(LIBSSH2_SESSION *session, int hash_type);
.fi
.SH DESCRIPTION
\fIsession\fP - Session instance as returned by
.BR libssh2_session_init_ex(3)

\fIhash_type\fP - One of: \fBLIBSSH2_HOSTKEY_HASH_MD5\fP,
\fBLIBSSH2_HOSTKEY_HASH_SHA1\fP or \fBLIBSSH2_HOSTKEY_HASH_SHA256\fP.

Returns the computed digest of the remote system's hostkey. The length of
the returned string is hash_type specific (e.g. 16 bytes for MD5,
20 bytes for SHA1, 32 bytes for SHA256).
.SH RETURN VALUE
Computed hostkey hash value, or NULL if the information is not available
(either the session has not yet been started up, or the requested hash
algorithm was not available). The hash consists of raw binary bytes, not hex
digits, so it is not directly printable.
.SH SEE ALSO
.BR libssh2_session_init_ex(3)
