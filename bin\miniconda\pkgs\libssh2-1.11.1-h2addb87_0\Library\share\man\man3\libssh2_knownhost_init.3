.\" Copyright (C) <PERSON>
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_knownhost_init 3 "28 May 2009" "libssh2" "libssh2"
.SH NAME
libssh2_knownhost_init - init a collection of known hosts
.SH SYNOPSIS
.nf
#include <libssh2.h>

LIBSSH2_KNOWNHOSTS *
libssh2_knownhost_init(LIBSSH2_SESSION *session);
.fi
.SH DESCRIPTION
Init a collection of known hosts for this session. Returns the handle to an
internal representation of a known host collection.

Call \fBlibssh2_knownhost_free(3)\fP to free the collection again after you are
doing using it.
.SH RETURN VALUE
Returns a handle pointer or NULL if something went wrong. The returned handle
is used as input to all other known host related functions libssh2 provides.
.SH AVAILABILITY
Added in libssh2 1.2
.SH SEE ALSO
.BR libssh2_knownhost_free(3)
.BR libssh2_knownhost_add(3)
.BR libssh2_knownhost_check(3)
