.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_crypto_engine 3 "22 Nov 2021" "libssh2" "libssh2"
.SH NAME
libssh2_crypto_engine - retrieve used crypto engine
.SH SYNOPSIS
.nf
#include <libssh2.h>

libssh2_crypto_engine_t
libssh2_crypto_engine(void);
.fi
.SH DESCRIPTION
Returns currently used crypto engine, as en enum value.
.SH AVAILABILITY
Added in libssh2 1.11
