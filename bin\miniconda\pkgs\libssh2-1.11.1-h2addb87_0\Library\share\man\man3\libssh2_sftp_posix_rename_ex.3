.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_sftp_posix_rename_ex 3 "9 May 2024" "libssh2 1.11.1" "libssh2"
.SH NAME
libssh2_sftp_posix_rename_ex - rename an SFTP file using POSIX semantics
.SH SYNOPSIS
.nf
#include <libssh2.h>
#include <libssh2_sftp.h>

int
libssh2_sftp_posix_rename_ex(LIBSSH2_SFTP *sftp,
                             const char *source_filename,
                             size_t source_filename_len,
                             const char *dest_filename,
                             size_t dest_filename_len);
.fi
.SH DESCRIPTION
\fIsftp\fP - SFTP instance as returned by
.BR libssh2_sftp_init(3)

\fIsourcefile\fP - Path and name of the existing filesystem entry

\fIsourcefile_len\fP - Length of the path and name of the existing
filesystem entry

\fIdestfile\fP - Path and name of the target filesystem entry

\fIdestfile_len\fP - Length of the path and name of the target
filesystem entry

This function <NAME_EMAIL> extension, which is
useful when, for example, moving files across filesystems on a remote server.
SSH_FXP_RENAME does not specify a specific implementation, but many servers
will attempt to user hard links when moving files using SSH_FXP_RENAME.

If the server does <NAME_EMAIL>, this function will
return LIBSSH2_FX_OP_UNSUPPORTED and you can call libssh2_sftp_rename_ex (3) as
a backup.
.SH RETURN VALUE
Return 0 on success or negative on failure. It returns
LIBSSH2_ERROR_EAGAIN when it would otherwise block. While
LIBSSH2_ERROR_EAGAIN is a negative number, it is not really a failure per se.
.SH ERRORS
\fILIBSSH2_FX_OP_UNSUPPORTED\fP - Server does not support
<EMAIL>

\fILIBSSH2_ERROR_ALLOC\fP - An internal memory allocation call failed.

\fILIBSSH2_ERROR_SOCKET_SEND\fP - Unable to send data on socket.

\fILIBSSH2_ERROR_SOCKET_TIMEOUT\fP -

\fILIBSSH2_ERROR_SFTP_PROTOCOL\fP - An invalid SFTP protocol response was
received on the socket, or an SFTP operation caused an errorcode to
be returned by the server.
.SH SEE ALSO
.BR libssh2_sftp_init(3)
