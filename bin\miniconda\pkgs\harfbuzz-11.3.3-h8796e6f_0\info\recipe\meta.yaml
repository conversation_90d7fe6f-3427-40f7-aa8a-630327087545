# This file created by conda-build 25.5.0
# meta.yaml template originally from:
# D:\a\1\s\recipe, last modified Tue Jul 29 13:20:12 2025
# ------------------------------------------------

package:
  name: harfbuzz
  version: 11.3.3
source:
  url: https://github.com/harfbuzz/harfbuzz/archive/11.3.3.tar.gz
  sha256: 5563e1eeea7399c37dc7f0f92a89bbc79d8741bbdd134d22d2885ddb95944314
  patches:
    - gir-windows.patch
build:
  number: '0'
  run_exports:
    - harfbuzz >=11.3.3
  string: h8796e6f_0
requirements:
  build:
    - _openmp_mutex 4.5 2_gnu
    - bzip2 1.0.8 h2466b09_7
    - ca-certificates 2025.7.14 h4c7d964_0
    - cairo 1.18.4 h5782bbf_0
    - font-ttf-dejavu-sans-mono 2.37 hab24e00_0
    - font-ttf-inconsolata 3.000 h77eed37_0
    - font-ttf-source-code-pro 2.038 h77eed37_0
    - font-ttf-ubuntu 0.83 h77eed37_3
    - fontconfig 2.15.0 h765892d_1
    - fonts-conda-ecosystem 1 0
    - fonts-conda-forge 1 0
    - freetype 2.13.3 h57928b3_1
    - g-ir-build-tools 1.84.0 py313h95dfb64_1
    - g-ir-host-tools 1.84.0 ha69a97b_1
    - gobject-introspection 1.84.0 py313h527ff07_1
    - icu 75.1 he0c23c2_0
    - libexpat 2.7.1 hac47afa_0
    - libffi 3.4.6 h537db12_1
    - libfreetype 2.13.3 h57928b3_1
    - libfreetype6 2.13.3 h0b5ce68_1
    - libgcc 15.1.0 h1383e82_3
    - libgirepository 1.84.0 h35b7f07_1
    - libglib 2.84.2 hbc94333_0
    - libgomp 15.1.0 h1383e82_3
    - libiconv 1.18 h135ad9c_1
    - libintl 0.22.5 h5728263_3
    - liblzma 5.8.1 h2466b09_2
    - libmpdec 4.0.0 h2466b09_0
    - libpng 1.6.50 h95bef1e_0
    - libsqlite 3.50.3 hf5d6505_1
    - libwinpthread 12.0.0.r4.gg4f2fc60ca h57928b3_9
    - libzlib 1.3.1 h2466b09_2
    - meson 1.8.2 pyhe01879c_0
    - ninja 1.13.1 h477610d_0
    - openssl 3.5.1 h725018a_0
    - pcre2 10.45 h99c9b8b_0
    - pixman 0.46.4 hc614b68_0
    - pkg-config 0.29.2 h88c491f_1009
    - pthread-stubs 0.4 h0e40799_1002
    - python 3.13.5 h7de537c_102_cp313
    - python_abi 3.13 8_cp313
    - setuptools 80.9.0 pyhff2d567_0
    - tk 8.6.13 h2c6b04d_2
    - tzdata 2025b h78e105d_0
    - ucrt 10.0.22621.0 h57928b3_1
    - vc 14.3 h41ae7f8_31
    - vc14_runtime 14.44.35208 h818238b_31
    - vcomp14 14.44.35208 h818238b_31
    - vs2019_win-64 19.29.30139 h7dcff83_31
    - vs_win-64 2019.11 h7dcff83_31
    - vswhere 3.1.7 h40126e0_1
  host:
    - _openmp_mutex 4.5 2_gnu
    - bzip2 1.0.8 h2466b09_7
    - ca-certificates 2025.7.14 h4c7d964_0
    - cairo 1.18.4 h5782bbf_0
    - expat 2.7.1 hac47afa_0
    - font-ttf-dejavu-sans-mono 2.37 hab24e00_0
    - font-ttf-inconsolata 3.000 h77eed37_0
    - font-ttf-source-code-pro 2.038 h77eed37_0
    - font-ttf-ubuntu 0.83 h77eed37_3
    - fontconfig 2.15.0 h765892d_1
    - fonts-conda-ecosystem 1 0
    - fonts-conda-forge 1 0
    - freetype 2.13.3 h57928b3_1
    - glib 2.84.2 he8f994d_0
    - glib-tools 2.84.2 h4394cf3_0
    - graphite2 1.3.14 he0c23c2_0
    - icu 75.1 he0c23c2_0
    - libexpat 2.7.1 hac47afa_0
    - libffi 3.4.6 h537db12_1
    - libfreetype 2.13.3 h57928b3_1
    - libfreetype6 2.13.3 h0b5ce68_1
    - libgcc 15.1.0 h1383e82_3
    - libglib 2.84.2 hbc94333_0
    - libgomp 15.1.0 h1383e82_3
    - libiconv 1.18 h135ad9c_1
    - libintl 0.22.5 h5728263_3
    - libintl-devel 0.22.5 h5728263_3
    - liblzma 5.8.1 h2466b09_2
    - libmpdec 4.0.0 h2466b09_0
    - libpng 1.6.50 h95bef1e_0
    - libsqlite 3.50.3 hf5d6505_1
    - libwinpthread 12.0.0.r4.gg4f2fc60ca h57928b3_9
    - libzlib 1.3.1 h2466b09_2
    - openssl 3.5.1 h725018a_0
    - packaging 25.0 pyh29332c3_1
    - pcre2 10.45 h99c9b8b_0
    - pixman 0.46.4 hc614b68_0
    - python 3.13.5 h7de537c_102_cp313
    - python_abi 3.13 8_cp313
    - tk 8.6.13 h2c6b04d_2
    - tzdata 2025b h78e105d_0
    - ucrt 10.0.22621.0 h57928b3_1
    - vc 14.3 h41ae7f8_31
    - vc14_runtime 14.44.35208 h818238b_31
    - vcomp14 14.44.35208 h818238b_31
    - xorg-xorgproto 2024.1 h0e40799_1
    - zlib 1.3.1 h2466b09_2
  run:
    - cairo >=1.18.4,<2.0a0
    - graphite2
    - icu >=75.1,<76.0a0
    - libexpat >=2.7.1,<3.0a0
    - libfreetype >=2.13.3
    - libfreetype6 >=2.13.3
    - libglib >=2.84.2,<3.0a0
    - libzlib >=1.3.1,<2.0a0
    - ucrt >=10.0.20348.0
    - vc >=14.2,<15
    - vc14_runtime >=14.29.30139
test:
  requires:
    - pygobject
  commands:
    - if not exist %PREFIX%\Library\bin\harfbuzz-cairo.dll exit 1
    - if not exist %PREFIX%\Library\bin\harfbuzz-gobject.dll exit 1
    - if not exist %PREFIX%\Library\bin\harfbuzz-icu.dll exit 1
    - if not exist %PREFIX%\Library\bin\harfbuzz-subset.dll exit 1
    - if not exist %PREFIX%\Library\bin\harfbuzz.dll exit 1
    - if not exist %PREFIX%\Library\include\harfbuzz\hb-ft.h exit 1
    - if not exist %PREFIX%\Library\lib\girepository-1.0\HarfBuzz-0.0.typelib exit
      1
about:
  home: https://harfbuzz.github.io/
  license: MIT
  license_file: COPYING
  summary: An OpenType text shaping engine.
  description: 'HarfBuzz is a text shaping library. New Harbuzz targets various font

    technologies while Old HarfBuzz targets only OpenType fonts.

    '
  doc_url: https://harfbuzz.github.io/
  dev_url: https://github.com/harfbuzz/harfbuzz
extra:
  recipe-maintainers:
    - ocefpaf
    - pkgw
    - tschoonj
  final: true
  copy_test_source_files: true
  flow_run_id: azure_20250729.1.1
  remote_url: https://github.com/conda-forge/harfbuzz-feedstock
  sha: f0e1db1ce0cc05bc365e04313ea4d9b710f3b153
