{"paths": [{"_path": "Library/include/nlohmann/adl_serializer.hpp", "path_type": "hardlink", "sha256": "0c4ce295a3f4092cff092901c340c961b10c48ec8d555110fe3df9f619740a19", "size_in_bytes": 2279}, {"_path": "Library/include/nlohmann/byte_container_with_subtype.hpp", "path_type": "hardlink", "sha256": "b56733d3a5b84004f4c7da8ac29ff030509d6a013eeb090541bfcdc46c1a5dc0", "size_in_bytes": 3533}, {"_path": "Library/include/nlohmann/detail/abi_macros.hpp", "path_type": "hardlink", "sha256": "702af5c60305125d8e5a4c813d271882b93bb2ba43cdfe91ee4edcef3fbeeb71", "size_in_bytes": 3807}, {"_path": "Library/include/nlohmann/detail/conversions/from_json.hpp", "path_type": "hardlink", "sha256": "8cad2ff4a90591110a5cc6134eef25e5a16c33f97b531e1576ed551319d16896", "size_in_bytes": 18880}, {"_path": "Library/include/nlohmann/detail/conversions/to_chars.hpp", "path_type": "hardlink", "sha256": "c2766aef57d381b2732a29a0f3facaf1c5a7bcbdda80542514ab834247f26c68", "size_in_bytes": 38503}, {"_path": "Library/include/nlohmann/detail/conversions/to_json.hpp", "path_type": "hardlink", "sha256": "2b87b28c17cef8ecc192a5137c67fd6c56a37746c7e478ca0aacbe354098016c", "size_in_bytes": 15749}, {"_path": "Library/include/nlohmann/detail/exceptions.hpp", "path_type": "hardlink", "sha256": "208d2c515d55bccadaab2d6d7609c4179b343e393137a5ebe1c96f293dfca101", "size_in_bytes": 9128}, {"_path": "Library/include/nlohmann/detail/hash.hpp", "path_type": "hardlink", "sha256": "54f7dd4f4d6cc7bd4c09fec7bda7e47992ee5799bf235104384ee70108023cff", "size_in_bytes": 4016}, {"_path": "Library/include/nlohmann/detail/input/binary_reader.hpp", "path_type": "hardlink", "sha256": "84ebe37454150101a5896d8f50ec1e2f0fc57e0cc9dc810190acd7d43ce9cb42", "size_in_bytes": 103174}, {"_path": "Library/include/nlohmann/detail/input/input_adapters.hpp", "path_type": "hardlink", "sha256": "581e5a0982c01bf63449479547e555101b70641dbfc65c31078e0fc4a4e61671", "size_in_bytes": 17372}, {"_path": "Library/include/nlohmann/detail/input/json_sax.hpp", "path_type": "hardlink", "sha256": "ee9c45bd7361bb98283bd1e9c87e87f639b3bd27a631769a2499962e03b05298", "size_in_bytes": 21370}, {"_path": "Library/include/nlohmann/detail/input/lexer.hpp", "path_type": "hardlink", "sha256": "723a7ae568a1460da5235cd1f844db6b12b0f684b5284d5a4c7db4dbe165c980", "size_in_bytes": 54544}, {"_path": "Library/include/nlohmann/detail/input/parser.hpp", "path_type": "hardlink", "sha256": "fd687a688fc68835ee1337f4e1e7e935bea6cd17dffabb43399628a69424d669", "size_in_bytes": 18722}, {"_path": "Library/include/nlohmann/detail/input/position_t.hpp", "path_type": "hardlink", "sha256": "9007ed8be12f1b0d001c292c1525fdeb1418bed8cd63bf8a00b118574f5bd5d1", "size_in_bytes": 958}, {"_path": "Library/include/nlohmann/detail/iterators/internal_iterator.hpp", "path_type": "hardlink", "sha256": "2414d89c20b4eb1278672a21b0f09858c4861ffe878432ec942e23e4d277479a", "size_in_bytes": 1071}, {"_path": "Library/include/nlohmann/detail/iterators/iter_impl.hpp", "path_type": "hardlink", "sha256": "db2f4f0f905ab2b80e8e01e96e177aeafe4d5a1a78b42facbc91fb79013eb794", "size_in_bytes": 23906}, {"_path": "Library/include/nlohmann/detail/iterators/iteration_proxy.hpp", "path_type": "hardlink", "sha256": "78fa90bcd26822953d451693b379b139c6f22629dbb64517ff3d58c0acb964ad", "size_in_bytes": 7962}, {"_path": "Library/include/nlohmann/detail/iterators/iterator_traits.hpp", "path_type": "hardlink", "sha256": "78a1c32b2bad6782f97063cf642a6dc02588960b9420c1924ca5ef76fb8df9d8", "size_in_bytes": 1757}, {"_path": "Library/include/nlohmann/detail/iterators/json_reverse_iterator.hpp", "path_type": "hardlink", "sha256": "3872b8f61110ff09a77c3a3dca852c0fee9defcaab5b778dc990a253883a5329", "size_in_bytes": 3861}, {"_path": "Library/include/nlohmann/detail/iterators/primitive_iterator.hpp", "path_type": "hardlink", "sha256": "274ba54c5c2b2cd8e7c1e635392b09e73fd65d4183571f821ad149b3954ed818", "size_in_bytes": 3227}, {"_path": "Library/include/nlohmann/detail/json_pointer.hpp", "path_type": "hardlink", "sha256": "cd86782e5a17b0b707fda35d5a39da85fc8c481e642b69ca1f95485131110c24", "size_in_bytes": 37067}, {"_path": "Library/include/nlohmann/detail/json_ref.hpp", "path_type": "hardlink", "sha256": "ef04660db0236f2f3341bd36de00cbc3f494018278800294be82f85e1fcc4b7c", "size_in_bytes": 1811}, {"_path": "Library/include/nlohmann/detail/macro_scope.hpp", "path_type": "hardlink", "sha256": "8d8a504d5ae8fcf34b676d9efd5eca07001bbb9a3417313b443c9d75018163f8", "size_in_bytes": 42861}, {"_path": "Library/include/nlohmann/detail/macro_unscope.hpp", "path_type": "hardlink", "sha256": "65bb2a65eb2935643f5b788efb7b4092f1a329d0b4fbbc9a1b7343775f58797f", "size_in_bytes": 1220}, {"_path": "Library/include/nlohmann/detail/meta/call_std/begin.hpp", "path_type": "hardlink", "sha256": "f79ecf122600e2e64f344dd95277910816206e37a80bed18737d22f281dbb465", "size_in_bytes": 453}, {"_path": "Library/include/nlohmann/detail/meta/call_std/end.hpp", "path_type": "hardlink", "sha256": "f0d1493594068a73102b52c47f48d3443ea589afbcaaa2672b0e71499f61d696", "size_in_bytes": 451}, {"_path": "Library/include/nlohmann/detail/meta/cpp_future.hpp", "path_type": "hardlink", "sha256": "519285d9ceca3d6d3afa0c647c71f8790b91179da66a08b68a9f73a8218016f3", "size_in_bytes": 5178}, {"_path": "Library/include/nlohmann/detail/meta/detected.hpp", "path_type": "hardlink", "sha256": "01963ca48427b32286a4bda3741c0817648af65eef673fe7cc9fc9d94cf343a8", "size_in_bytes": 2109}, {"_path": "Library/include/nlohmann/detail/meta/identity_tag.hpp", "path_type": "hardlink", "sha256": "9a95aaad68fe15b01570b4f9efaebd1ed2e5ad703767f4b93fc2cd855142199c", "size_in_bytes": 526}, {"_path": "Library/include/nlohmann/detail/meta/is_sax.hpp", "path_type": "hardlink", "sha256": "876aaf9f4ccbed679a9340cf51b3dbe38eaf0b18122e0affe0c5e711a2f73533", "size_in_bytes": 6960}, {"_path": "Library/include/nlohmann/detail/meta/std_fs.hpp", "path_type": "hardlink", "sha256": "68e8e979cca53bd97bc2e85b9e8a78066b5e791a951b0c73290cd7e5c740bc6a", "size_in_bytes": 766}, {"_path": "Library/include/nlohmann/detail/meta/type_traits.hpp", "path_type": "hardlink", "sha256": "b13c458f9cd6a2d8577031f12e11a951e4ae7b508cce64ffadff42f248ad8b4b", "size_in_bytes": 28099}, {"_path": "Library/include/nlohmann/detail/meta/void_t.hpp", "path_type": "hardlink", "sha256": "30cf458ad751b37097da34c3e28d9e387bbb37fab29d180984baba4e67d3a831", "size_in_bytes": 597}, {"_path": "Library/include/nlohmann/detail/output/binary_writer.hpp", "path_type": "hardlink", "sha256": "a6ccc235e52fa612c2717e278939ed1632c456e96881805d084847cf3cc3d06d", "size_in_bytes": 69893}, {"_path": "Library/include/nlohmann/detail/output/output_adapters.hpp", "path_type": "hardlink", "sha256": "fec93f451953874aad7a30c79b921cd0c4ade1698e36e964bfe89f1a4a6a3c0f", "size_in_bytes": 4067}, {"_path": "Library/include/nlohmann/detail/output/serializer.hpp", "path_type": "hardlink", "sha256": "5eaa3faae3791c53ada6800967e166cad8159bd9545b5abdfc18cda5ea9826e4", "size_in_bytes": 39896}, {"_path": "Library/include/nlohmann/detail/string_concat.hpp", "path_type": "hardlink", "sha256": "33a2a25c74cb00124b1e69108a06b4c2bb02fd369d40bfe5aed56c401c8f5011", "size_in_bytes": 6006}, {"_path": "Library/include/nlohmann/detail/string_escape.hpp", "path_type": "hardlink", "sha256": "16899a2c01c9aee2cd0af5b9ede0d3f579af00b58303197e2692f756310317c9", "size_in_bytes": 2168}, {"_path": "Library/include/nlohmann/detail/value_t.hpp", "path_type": "hardlink", "sha256": "f16e2a05c4fdfbe22fc2cee90f7adc0e6ca2c5345348fa1f8c855a8e04f263b6", "size_in_bytes": 4326}, {"_path": "Library/include/nlohmann/json.hpp", "path_type": "hardlink", "sha256": "bbf96a24a8d59ef79467761e2a803537e1476909bb7d6baed23094a1833c088c", "size_in_bytes": 193862}, {"_path": "Library/include/nlohmann/json_fwd.hpp", "path_type": "hardlink", "sha256": "7b116cea514a11fdb71da0bd6a4273b2816750d29979711313e4a76a8fe5bd02", "size_in_bytes": 2469}, {"_path": "Library/include/nlohmann/ordered_map.hpp", "path_type": "hardlink", "sha256": "9d7070944cc64da4818120698975b74d8b7e61152b5d10aff81985493d5b4f6d", "size_in_bytes": 11447}, {"_path": "Library/include/nlohmann/thirdparty/hedley/hedley.hpp", "path_type": "hardlink", "sha256": "6c5cdf981e43776f4cd320dc7476309efa7332f457288c4f33051b7fa4f7611d", "size_in_bytes": 86068}, {"_path": "Library/include/nlohmann/thirdparty/hedley/hedley_undef.hpp", "path_type": "hardlink", "sha256": "2e5aff1a15cbfd8786a2776d0ec0e38e14d2ceadeea229bce5c583bafc26803c", "size_in_bytes": 5500}, {"_path": "Library/include/test_data.hpp", "path_type": "hardlink", "sha256": "06a6ba0e5a4d2fc5013eb7ca8585dbe45d45546fe7b62a8ce88c088dd82c2cab", "size_in_bytes": 105}, {"_path": "Library/nlohmann_json.natvis", "path_type": "hardlink", "sha256": "9a4c408d6180233221cb9bc40c4bfe3ed7e518194321603590728228a93b0110", "size_in_bytes": 19576}, {"_path": "Library/share/cmake/nlohmann_json/nlohmann_jsonConfig.cmake", "path_type": "hardlink", "sha256": "a879dc2b648e17ac32836ef9013b5a6cf1234a74b9a60962cc081c3776c9f13f", "size_in_bytes": 635}, {"_path": "Library/share/cmake/nlohmann_json/nlohmann_jsonConfigVersion.cmake", "path_type": "hardlink", "sha256": "c78c5861db49fb6bad5dc9885fb1d375a98192e558b503bd95faa27e8cb0810c", "size_in_bytes": 696}, {"_path": "Library/share/cmake/nlohmann_json/nlohmann_jsonTargets.cmake", "path_type": "hardlink", "sha256": "e862e30d7441e378e221979af1ee3da4818d92485367bc435edc7a6cc02d0bd6", "size_in_bytes": 3971}, {"_path": "Library/share/pkgconfig/nlo<PERSON>_json.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:/b/abs_b12svzu_88/croot/n<PERSON><PERSON>_json_1680083453514/_h_env", "sha256": "9a7330237cdc9cb72de838e7bf612cd28b2c3786b8806b2c6ad0a0faa1ecfddf", "size_in_bytes": 160}], "paths_version": 1}