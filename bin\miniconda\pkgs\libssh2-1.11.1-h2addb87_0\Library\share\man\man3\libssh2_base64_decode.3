.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_base64_decode 3 "23 Dec 2008" "libssh2 1.0" "libssh2"
.SH NAME
libssh2_base64_decode - decode a base64 encoded string
.SH SYNOPSIS
.nf
#include <libssh2.h>

int
libssh2_base64_decode(LIBSSH2_SESSION *session, char **dest,
                      unsigned int *dest_len, const char *src,
                      unsigned int src_len);
.fi
.SH DESCRIPTION
This function is deemed DEPRECATED and will be removed from libssh2 in a
future version. Do not use it!

Decode a base64 chunk and store it into a newly allocated buffer. 'dest_len'
will be set to hold the length of the returned buffer that '*dest' will point
to.

The returned buffer is allocated by this function, but it is not clear how to
free that memory!
.SH BUGS
The memory that *dest points to is allocated by the malloc function libssh2
uses, but there is no way for an application to free this data in a safe and
reliable way!
.SH RETURN VALUE
0 if successful, \-1 if any error occurred.
