{"channels": ["https://repo.anaconda.com/pkgs/main"], "conda_build_version": "3.21.9", "conda_private": false, "conda_version": "4.13.0", "env_vars": {"CIO_TEST": "<not set>"}, "extra": {"copy_test_source_files": true, "final": true, "flow_run_id": "31e74ecb-fb68-410f-9cf3-0b1553d18306", "recipe-maintainers": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "remote_url": "**************:AnacondaRecipes/mdurl-feedstock.git", "sha": "f6f2c5333f81f9e6e80de5c3c7e46b4f565229b9"}, "home": "https://github.com/executablebooks/mdurl", "identifiers": [], "keywords": [], "license": "MIT", "license_family": "MIT", "license_file": "LICENSE", "root_pkgs": ["anaconda-client 1.10.0 py39haa95532_0", "appdirs 1.4.4 pyhd3eb1b0_0", "asn1crypto 1.5.1 py39haa95532_0", "attrs 21.4.0 pyhd3eb1b0_0", "beautifulsoup4 4.11.1 py39haa95532_0", "boto3 1.24.28 py39haa95532_0", "botocore 1.27.28 py39haa95532_0", "brotlipy 0.7.0 py39h2bbff1b_1003", "bzip2 1.0.8 he774522_0", "ca-certificates 2022.07.19 haa95532_0", "certifi 2022.6.15 py39haa95532_0", "cffi 1.15.0 py39h2bbff1b_1", "chardet 4.0.0 py39haa95532_1003", "charset-normalizer 2.0.4 pyhd3eb1b0_0", "click 8.0.4 py39haa95532_0", "cloudpickle 2.0.0 pyhd3eb1b0_0", "clyent 1.2.2 py39haa95532_1", "colorama 0.4.5 py39haa95532_0", "conda 4.13.0 py39haa95532_0", "conda-build 3.21.9 py39haa95532_0", "conda-package-handling 1.8.1 py39h8cc25b3_0", "console_shortcut 0.1.1 4", "croniter 0.3.35 py_0", "cryptography 37.0.1 py39h21b164f_0", "dask-core 2022.7.0 py39haa95532_0", "deprecated 1.2.12 pyhd3eb1b0_0", "distributed 2022.7.0 py39haa95532_0", "docker-py 4.4.1 py39haa95532_5", "docker-pycreds 0.4.0 pyhd3eb1b0_0", "filelock 3.6.0 pyhd3eb1b0_0", "fsspec 2022.3.0 py39haa95532_0", "git 2.34.1 haa95532_0", "glob2 0.7 pyhd3eb1b0_0", "heapdict 1.0.1 pyhd3eb1b0_0", "idna 3.3 pyhd3eb1b0_0", "importlib_resources 5.2.0 pyhd3eb1b0_1", "jinja2 2.11.3 pyhd3eb1b0_0", "jmespath 0.10.0 pyhd3eb1b0_0", "jsonschema 4.4.0 py39haa95532_0", "jupyter_core 4.10.0 py39haa95532_0", "libarchive 3.5.2 h214662b_0", "libiconv 1.16 h2bbff1b_2", "liblief 0.11.5 hd77b12b_1", "libxml2 2.9.14 h0ad7f3c_0", "locket 1.0.0 py39haa95532_0", "lz4-c 1.9.3 h2bbff1b_1", "markupsafe 2.0.1 py39h2bbff1b_0", "marshmallow 3.12.2 pyhd3eb1b0_0", "marshmallow-oneofschema 3.0.1 pyhd3eb1b0_0", "menuinst 1.4.18 py39h59b6b97_0", "msgpack-python 1.0.3 py39h59b6b97_0", "mypy_extensions 0.4.3 py39haa95532_1", "natsort 7.1.1 pyhd3eb1b0_0", "nbformat 5.3.0 py39haa95532_0", "openssl 1.1.1q h2bbff1b_0", "packaging 21.3 pyhd3eb1b0_0", "partd 1.2.0 pyhd3eb1b0_1", "pendulum 2.1.2 pyhd3eb1b0_1", "pip 22.1.2 py39haa95532_0", "pkginfo 1.8.2 pyhd3eb1b0_0", "powershell_shortcut 0.0.1 3", "prefect 1.2.1 pyhd3eb1b0_0", "psutil 5.9.0 py39h2bbff1b_0", "py-lief 0.11.5 py39hd77b12b_1", "pycosat 0.6.3 py39h2bbff1b_0", "pycparser 2.21 pyhd3eb1b0_0", "pygithub 1.55 pyhd3eb1b0_1", "pyjwt 2.4.0 py39haa95532_0", "pynacl 1.4.0 py39hbd8134f_1", "pyopenssl 22.0.0 pyhd3eb1b0_0", "pyparsing 3.0.4 pyhd3eb1b0_0", "pyrsistent 0.18.0 py39h196d8e1_0", "pysocks 1.7.1 py39haa95532_0", "python 3.9.12 h6244533_0", "python-box 5.4.1 pyhd3eb1b0_0", "python-dateutil 2.8.2 pyhd3eb1b0_0", "python-fastjsonschema 2.15.1 pyhd3eb1b0_0", "python-json-logger 2.0.1 py_0", "python-libarchive-c 2.9 pyhd3eb1b0_1", "python-slugify 5.0.2 pyhd3eb1b0_0", "pytz 2022.1 py39haa95532_0", "pytzdata 2020.1 pyhd3eb1b0_0", "pywin32 302 py39h2bbff1b_2", "pyyaml 6.0 py39h2bbff1b_1", "requests 2.28.1 py39haa95532_0", "ruamel.yaml 0.16.12 py39h2bbff1b_1", "ruamel.yaml.clib 0.2.0 py39h2bbff1b_0", "ruamel_yaml 0.15.100 py39h2bbff1b_0", "s3transfer 0.6.0 py39haa95532_0", "setuptools 61.2.0 py39haa95532_0", "six 1.16.0 pyhd3eb1b0_1", "sortedcontainers 2.4.0 pyhd3eb1b0_0", "soupsieve 2.3.1 pyhd3eb1b0_0", "sqlite 3.38.5 h2bbff1b_0", "tabulate 0.8.10 py39haa95532_0", "tblib 1.7.0 pyhd3eb1b0_0", "text-unidecode 1.3 pyhd3eb1b0_0", "toml 0.10.2 pyhd3eb1b0_0", "toolz 0.11.2 pyhd3eb1b0_0", "tornado 6.1 py39h2bbff1b_0", "tqdm 4.64.0 py39haa95532_0", "traitlets 5.1.1 pyhd3eb1b0_0", "tzdata 2022a hda174b7_0", "unidecode 1.2.0 pyhd3eb1b0_0", "urllib3 1.26.11 py39haa95532_0", "vc 14.2 h21ff451_1", "vs2015_runtime 14.27.29016 h5e58377_2", "websocket-client 0.58.0 py39haa95532_4", "wheel 0.37.1 pyhd3eb1b0_0", "wincertstore 0.2 py39haa95532_2", "win_inet_pton 1.1.0 py39haa95532_0", "wrapt 1.14.1 py39h2bbff1b_0", "xz 5.2.5 h8cc25b3_1", "yaml 0.2.5 he774522_0", "zict 2.1.0 py39haa95532_0", "zipp 3.8.0 py39haa95532_0", "zlib 1.2.12 h8cc25b3_2", "zstd 1.5.2 h19a0ad4_0"], "summary": "URL utilities for markdown-it-py parser.", "tags": []}