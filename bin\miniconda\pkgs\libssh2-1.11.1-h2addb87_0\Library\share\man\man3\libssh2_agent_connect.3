.\" Copyright (C) <PERSON><PERSON>
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_agent_connect 3 "23 Dec 2009" "libssh2" "libssh2"
.SH NAME
libssh2_agent_connect - connect to an ssh-agent
.SH SYNOPSIS
.nf
#include <libssh2.h>

int
libssh2_agent_connect(LIBSSH2_AGENT *agent);
.fi
.SH DESCRIPTION
Connect to an ssh-agent running on the system.

Call \fBlibssh2_agent_disconnect(3)\fP to close the connection after
you are doing using it.
.SH RETURN VALUE
Returns 0 if succeeded, or a negative value for error.
.SH AVAILABILITY
Added in libssh2 1.2
.SH SEE ALSO
.BR libssh2_agent_init(3)
.BR libssh2_agent_disconnect(3)
