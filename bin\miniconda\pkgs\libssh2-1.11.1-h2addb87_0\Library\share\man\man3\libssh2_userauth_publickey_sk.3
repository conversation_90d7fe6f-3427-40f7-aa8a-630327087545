.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_userauth_publickey_sk 3 "1 Jun 2022" "libssh2" "libssh2"
.SH NAME
libssh2_userauth_publickey_sk - authenticate a session with a FIDO2 authenticator
.SH SYNOPSIS
.nf
#include <libssh2.h>

int
libssh2_userauth_publickey_sk(LIBSSH2_SESSION *session,
                              const char *username,
                              size_t username_len,
                              const unsigned char *publickeydata,
                              size_t publickeydata_len,
                              const char *privatekeydata,
                              size_t privatekeydata_len,
                              const char *passphrase,
                              LIBSSH2_USERAUTH_SK_SIGN_FUNC((*sign_callback)),
                              void **abstract);
.fi
.SH CALLBACK
.nf
#define LIBSSH2_SK_PRESENCE_REQUIRED     0x01
#define LIBSSH2_SK_VERIFICATION_REQUIRED 0x04

typedef struct _LIBSSH2_SK_SIG_INFO {
    uint8_t flags;
    uint32_t counter;
    unsigned char *sig_r;
    size_t sig_r_len;
    unsigned char *sig_s;
    size_t sig_s_len;
} LIBSSH2_SK_SIG_INFO;

int name(LIBSSH2_SESSION *session, LIBSSH2_SK_SIG_INFO *sig_info,
         const unsigned char *data, size_t data_len, int algorithm,
         uint8_t flags, const char *application,
         const unsigned char *key_handle, size_t handle_len,
         void **abstract);
.fi
.SH DESCRIPTION
\fIsession\fP - Session instance as returned by
.BR libssh2_session_init_ex(3)

\fIusername\fP - Name of user to attempt authentication for.

\fIusername_len\fP - Length of username parameter.

\fIpublickeydata\fP - Buffer containing the contents of a public key file. If
NULL, the public key will be extracted from the privatekeydata. When using
certificate authentication, this buffer should contain the public certificate
data.

\fIpublickeydata_len\fP - Length of public key data.

\fIprivatekeydata\fP - Buffer containing the contents of a private key file.

\fIprivatekeydata_len\fP - Length of private key data.

\fIpassphrase\fP - Passphrase to use when decoding private key file.

\fIsign_callback\fP - Callback to communicate with FIDO2 authenticator.

\fIabstract\fP - User-provided data to pass to callback.

Attempt FIDO2 authentication. using <NAME_EMAIL> or
<EMAIL> key exchange algorithms.

This function is only supported when libssh2 is backed by OpenSSL.

.SH CALLBACK DESCRIPTION
\fIsession\fP - Session instance as returned by
.BR libssh2_session_init_ex(3)

\fIsig_info\fP - Filled in by the callback with the signature and accompanying
information from the authenticator.

\fIdata\fP - The data to sign.

\fIdata_len\fP - The length of the data parameter.

\fIalgorithm\fP - The signing algorithm to use. Possible values are
LIBSSH2_HOSTKEY_TYPE_ED25519 and LIBSSH2_HOSTKEY_TYPE_ECDSA_256.

\fIflags\fP - A bitmask specifying options for the authenticator. When
LIBSSH2_SK_PRESENCE_REQUIRED is set, the authenticator requires a touch. When
LIBSSH2_SK_VERIFICATION_REQUIRED is set, the authenticator requires a PIN.
Many servers and authenticators do not work properly when
LIBSSH2_SK_PRESENCE_REQUIRED is not set.

\fIapplication\fP - A user-defined string to use as the RP name for the
authenticator. Usually "ssh:".

\fIkey_handle\fP - The key handle to use for the authenticator's allow list.

\fIhandle_len\fP - The length of the key_handle parameter.

\fIabstract\fP - User-defined data. When a PIN is required, use this to pass in
the PIN, or a function pointer to retrieve the PIN.

The \fIsign_callback\fP is responsible for communicating with the hardware
authenticator to generate a signature. On success, the signature information
must be placed in the `\fIsig_info\fP sig_info parameter and the callback must
return 0. On failure, it should return a negative number.

The fields of the LIBSSH2_SK_SIG_INFO are as follows.

\fIflags\fP - A bitmask specifying options for the authenticator. This should
be read from the authenticator and not merely copied from the flags parameter
to the callback.

\fIcounter\fP - A value returned from the authenticator.

\fIsig_r\fP - For Ed25519 signatures, this contains the entire signature, as
returned directly from the authenticator. For ECDSA signatures, this contains
the r component of the signature in a big-endian binary representation. For
both algorithms, use LIBSSH2_ALLOC to allocate memory. It will be freed by the
caller.

\fIsig_r_len\fP - The length of the sig_r parameter.

\fIsig_s\fP - For ECDSA signatures, this contains the s component of the
signature in a big-endian binary representation. Use LIBSSH2_ALLOC to allocate
memory. It will be freed by the caller. For Ed25519 signatures, set this to
NULL.

\fIsig_s_len\fP - The length of the sig_s parameter.
.SH RETURN VALUE
Return 0 on success or negative on failure. It returns
LIBSSH2_ERROR_EAGAIN when it would otherwise block. While
LIBSSH2_ERROR_EAGAIN is a negative number, it is not really a failure per se.
.SH ERRORS
Some of the errors this function may return include:

\fILIBSSH2_ERROR_ALLOC\fP - An internal memory allocation call failed.

\fILIBSSH2_ERROR_SOCKET_SEND\fP - Unable to send data on socket.

\fILIBSSH2_ERROR_AUTHENTICATION_FAILED\fP - failed, invalid username/key.
.SH AVAILABILITY
Added in libssh2 1.10.0
.SH SEE ALSO
.BR libssh2_session_init_ex(3)
