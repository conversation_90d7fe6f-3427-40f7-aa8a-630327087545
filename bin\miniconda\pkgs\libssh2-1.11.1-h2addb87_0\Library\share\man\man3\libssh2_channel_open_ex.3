.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_channel_open_ex 3 "1 Jun 2007" "libssh2 0.15" "libssh2"
.SH NAME
libssh2_channel_open_ex - establish a generic session channel
.SH SYNOPSIS
.nf
#include <libssh2.h>

LIBSSH2_CHANNEL *
libssh2_channel_open_ex(LIBSSH2_SESSION *session, const char *channel_type,
                        unsigned int channel_type_len,
                        unsigned int window_size,
                        unsigned int packet_size,
                        const char *message, unsigned int message_len);

LIBSSH2_CHANNEL *
libssh2_channel_open_session(session);
.fi
.SH DESCRIPTION
\fIsession\fP - Session instance as returned by
.BR libssh2_session_init_ex(3)

\fIchannel_type\fP - Channel type to open. Typically one of session,
direct-tcpip, or tcpip-forward. The SSH2 protocol allowed for additional
types including local, custom channel types.

\fIchannel_type_len\fP - Length of channel_type

\fIwindow_size\fP - Maximum amount of unacknowledged data remote host is
allowed to send before receiving an SSH_MSG_CHANNEL_WINDOW_ADJUST packet.

\fIpacket_size\fP - Maximum number of bytes remote host is allowed to send
in a single SSH_MSG_CHANNEL_DATA or SSG_MSG_CHANNEL_EXTENDED_DATA packet.

\fImessage\fP - Additional data as required by the selected channel_type.

\fImessage_len\fP - Length of message parameter.

Allocate a new channel for exchanging data with the server. This method is
typically called through its macroized form:
.BR libssh2_channel_open_session(3)
or via
.BR libssh2_channel_direct_tcpip(3)
or
.BR libssh2_channel_forward_listen(3)
.SH RETURN VALUE
Pointer to a newly allocated LIBSSH2_CHANNEL instance, or NULL on errors.
.SH ERRORS
\fILIBSSH2_ERROR_ALLOC\fP - An internal memory allocation call failed.

\fILIBSSH2_ERROR_SOCKET_SEND\fP - Unable to send data on socket.

\fILIBSSH2_ERROR_CHANNEL_FAILURE\fP -

\fILIBSSH2_ERROR_EAGAIN\fP - Marked for non-blocking I/O but the call would block.
.SH SEE ALSO
Add related functions
