.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_sftp_last_error 3 "1 Jun 2007" "libssh2 0.15" "libssh2"
.SH NAME
libssh2_sftp_last_error - return the last SFTP-specific error code
.SH SYNOPSIS
.nf
#include <libssh2.h>
#include <libssh2_sftp.h>

unsigned long
libssh2_sftp_last_error(LIBSSH2_SFTP *sftp);
.fi
.SH DESCRIPTION
\fIsftp\fP - SFTP instance as returned by
.BR libssh2_sftp_init(3)

Returns the last error code produced by the SFTP layer. Note that this only
returns a sensible error code if libssh2 returned LIBSSH2_ERROR_SFTP_PROTOCOL
in a previous call. Using \fBlibssh2_sftp_last_error(3)\fP without a
preceding SFTP protocol error, it will return an unspecified value.
.SH RETURN VALUE
Current error code state of the SFTP instance.
.SH SEE ALSO
.BR libssh2_sftp_init(3)
