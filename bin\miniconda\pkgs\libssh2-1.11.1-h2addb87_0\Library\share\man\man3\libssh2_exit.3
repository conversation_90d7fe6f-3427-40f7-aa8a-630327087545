.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_exit 3 "19 Mar 2010" "libssh2" "libssh2"
.SH NAME
libssh2_exit - global library deinitialization
.SH SYNOPSIS
.nf
#include <libssh2.h>

void
libssh2_exit(void);
.fi
.SH DESCRIPTION
Exit the libssh2 functions and frees all memory used internal.
.SH AVAILABILITY
Added in libssh2 1.2.5
.SH SEE ALSO
.BR libssh2_init(3)
