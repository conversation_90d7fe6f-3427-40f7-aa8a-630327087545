CI: azure
CONDA_BUILD_SKIP_TESTS: ''
c_compiler: vs2019
c_stdlib: vs
cairo: '1'
channel_sources: conda-forge
channel_targets: conda-forge main
cpu_optimization_target: nocona
cran_mirror: https://cran.r-project.org
cxx_compiler: vs2019
expat: '2'
extend_keys:
- pin_run_as_build
- ignore_version
- extend_keys
- ignore_build_only_deps
fortran_compiler: gfortran
freetype: '2'
glib: '2'
harfbuzz: 11.0.1
icu: '75'
ignore_build_only_deps:
- numpy
- python
lua: '5'
numpy: '1.26'
perl: 5.26.2
pin_run_as_build:
  python:
    max_pin: x.x
    min_pin: x.x
  r-base:
    max_pin: x.x
    min_pin: x.x
python: '3.12'
r_base: '3.4'
target_platform: win-64
vc: '14'
zlib: '1'
