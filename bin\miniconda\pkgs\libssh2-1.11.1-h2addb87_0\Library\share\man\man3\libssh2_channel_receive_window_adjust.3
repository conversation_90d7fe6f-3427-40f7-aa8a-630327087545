.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_channel_receive_window_adjust 3 "15 Mar 2009" "libssh2 0.15" "libssh2"
.SH NAME
libssh2_channel_receive_window_adjust - adjust the channel window
.SH SYNOPSIS
.nf
#include <libssh2.h>

unsigned long
libssh2_channel_receive_window_adjust(LIBSSH2_CHANNEL * channel,
                                      unsigned long adjustment,
                                      unsigned char force);
.fi
.SH DESCRIPTION
This function is \fBDEPRECATED\fP in 1.1.0. Use the
\fIlibssh2_channel_receive_window_adjust2(3)\fP function instead!

Adjust the receive window for a channel by adjustment bytes. If the amount to
be adjusted is less than LIBSSH2_CHANNEL_MINADJUST and force is 0 the
adjustment amount will be queued for a later packet.
.SH RETURN VALUE
Returns the new size of the receive window (as understood by remote end). Note
that the window value sent over the wire is strictly 32bit, but this API is
made to return a 'long' which may not be 32 bit on all platforms.
.SH ERRORS
In 1.0 and earlier, this function returns LIBSSH2_ERROR_EAGAIN for
non-blocking channels where it would otherwise block. However, that is a
negative number and this function only returns an unsigned value and this then
leads to a very strange value being returned.
.SH SEE ALSO
.BR libssh2_channel_window_read_ex(3)
