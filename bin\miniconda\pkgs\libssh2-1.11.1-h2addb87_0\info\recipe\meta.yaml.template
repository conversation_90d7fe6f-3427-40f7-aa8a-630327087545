{% set name = "libssh2" %}
{% set version = "1.11.1" %}

package:
  name: {{ name }}
  version: {{ version }}

source:
  url: https://www.libssh2.org/download/{{ name }}-{{ version }}.tar.gz
  sha256: d9ec76cbe34db98eec3539fe2c899d26b0c837cb3eb466a56b0f109cabf658f7

build:
  number: 0
  run_exports:
    - {{ pin_subpackage('libssh2') }}

requirements:
  build:
    - {{ compiler('c') }}
    # This breaks a dependency cycle:
    # curl->libssh2->cmake->curl
    - cmake-no-system
    - ninja-base
  host:
    - openssl {{ openssl }}
    - zlib {{ zlib }}
  run:
    # exact pin handled through openssl and zlib run_exports
    - openssl
    - zlib

test:
  commands:
    - test -f $PREFIX/include/libssh2.h              # [not win]
    - test -f $PREFIX/include/libssh2_publickey.h    # [not win]
    - test -f $PREFIX/include/libssh2_sftp.h         # [not win]

    - test -f $PREFIX/lib/libssh2${SHLIB_EXT}        # [not win]
    - test -f $PREFIX/lib/pkgconfig/libssh2.pc       # [not win]

    - if not exist %LIBRARY_INC%\\libssh2.h exit 1              # [win]
    - if not exist %LIBRARY_INC%\\libssh2_publickey.h exit 1    # [win]
    - if not exist %LIBRARY_INC%\\libssh2_sftp.h exit 1         # [win]
    - if not exist %LIBRARY_LIB%\\libssh2.lib exit 1            # [win]
    - if not exist %LIBRARY_LIB%\\pkgconfig\\libssh2.pc exit 1  # [win]
    - if not exist %LIBRARY_BIN%\\libssh2.dll exit 1            # [win]

about:
  home: https://www.libssh2.org/
  license: BSD-3-Clause
  license_family: BSD
  license_file: COPYING
  summary: 'the SSH library'
  description: |
    libssh2 is a library implementing the SSH2 protocol, available under the revised BSD license.
  doc_url: https://www.libssh2.org/docs.html
  dev_url: https://github.com/libssh2/libssh2

extra:
  recipe-maintainers:
    - shadowwalkersb
