.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_sftp_statvfs 3 "22 May 2010" "libssh2" "libssh2"
.SH NAME
libssh2_sftp_statvfs, libssh2_sftp_fstatvfs - get file system statistics
.SH SYNOPSIS
.nf
#include <libssh2.h>
#include <libssh2_sftp.h>

int
libssh2_sftp_statvfs(LIBSSH2_SFTP *sftp, const char *path,
                     size_t path_len, LIBSSH2_SFTP_STATVFS *st);

int
libssh2_sftp_fstatvfs(LIBSSH2_SFTP_HANDLE *handle,
                      LIBSSH2_SFTP_STATVFS *st)
.fi
.SH DESCRIPTION
These functions provide statvfs(2)-like operations and require
statvfs@openssh.<NAME_EMAIL> extension support on the server.

\fIsftp\fP - SFTP instance as returned by
.BR libssh2_sftp_init(3)

\fIhandle\fP - SFTP File Handle as returned by
.BR libssh2_sftp_open_ex(3)

\fIpath\fP - full path of any file within the mounted file system.

\fIpath_len\fP - length of the full path.

\fIst\fP - Pointer to a LIBSSH2_SFTP_STATVFS structure to place file system
statistics into.
.SH DATA TYPES
LIBSSH2_SFTP_STATVFS is a typedefed struct that is defined as below

.nf
struct _LIBSSH2_SFTP_STATVFS {
    libssh2_uint64_t  f_bsize;    /* file system block size */
    libssh2_uint64_t  f_frsize;   /* fragment size */
    libssh2_uint64_t  f_blocks;   /* size of fs in f_frsize units */
    libssh2_uint64_t  f_bfree;    /* # free blocks */
    libssh2_uint64_t  f_bavail;   /* # free blocks for non-root */
    libssh2_uint64_t  f_files;    /* # inodes */
    libssh2_uint64_t  f_ffree;    /* # free inodes */
    libssh2_uint64_t  f_favail;   /* # free inodes for non-root */
    libssh2_uint64_t  f_fsid;     /* file system ID */
    libssh2_uint64_t  f_flag;     /* mount flags */
    libssh2_uint64_t  f_namemax;  /* maximum filename length */
};
.fi

It is unspecified whether all members of the returned struct have meaningful
values on all file systems.

The field \fIf_flag\fP is a bit mask. Bits are defined as follows:
.IP LIBSSH2_SFTP_ST_RDONLY
Read-only file system.
.IP LIBSSH2_SFTP_ST_NOSUID
Set-user-ID/set-group-ID bits are ignored by \fBexec\fP(3).
.SH RETURN VALUE
Returns 0 on success or negative on failure. If used in non-blocking mode, it
returns LIBSSH2_ERROR_EAGAIN when it would otherwise block. While
LIBSSH2_ERROR_EAGAIN is a negative number, it is not really a failure per se.
.SH ERRORS
\fILIBSSH2_ERROR_ALLOC\fP - An internal memory allocation call failed.

\fILIBSSH2_ERROR_SOCKET_SEND\fP - Unable to send data on socket.

\fILIBSSH2_ERROR_SOCKET_TIMEOUT\fP -

\fILIBSSH2_ERROR_SFTP_PROTOCOL\fP - An invalid SFTP protocol response was
received on the socket, or an SFTP operation caused an errorcode to be returned
by the server.
.SH AVAILABILITY
Added in libssh2 1.2.6
.SH SEE ALSO
.BR libssh2_sftp_open_ex(3)
