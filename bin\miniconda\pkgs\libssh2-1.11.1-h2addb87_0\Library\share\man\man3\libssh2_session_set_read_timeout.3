.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_session_set_read_timeout 3 "13 Jan 2023" "libssh2" "libssh2"
.SH NAME
libssh2_session_set_read_timeout - set timeout for packet read functions
.SH SYNOPSIS
.nf
#include <libssh2.h>

void
libssh2_session_set_read_timeout(LIBSSH2_SESSION *session, long timeout);
.fi
.SH DESCRIPTION
Set the \fBtimeout\fP in seconds for how long libssh2 packet read
function calls may wait until they consider the situation an error and return
LIBSSH2_ERROR_TIMEOUT.

By default or if you set the timeout to zero, the timeout will be set to
60 seconds.
.SH RETURN VALUE
Nothing
.SH AVAILABILITY
Added in 1.10.1
.SH SEE ALSO
.BR libssh2_session_get_read_timeout(3)
