.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_session_methods 3 "8 Nov 2021" "libssh2 1.11" "libssh2"
.SH NAME
libssh2_session_methods - return the currently active algorithms
.SH SYNOPSIS
.nf
#include <libssh2.h>

const char *
libssh2_session_methods(LIBSSH2_SESSION *session, int method_type);
.fi
.SH DESCRIPTION
\fIsession\fP - Session instance as returned by
.BR libssh2_session_init_ex(3)

\fImethod_type\fP - one of the method type constants: LIBSSH2_METHOD_KEX,
LIBSSH2_METHOD_HOSTKEY, LIBSSH2_METHOD_CRYPT_CS, LIBSSH2_METHOD_CRYPT_SC,
LIBSSH2_METHOD_MAC_CS, LIBSSH2_METHOD_MAC_SC, LIBSSH2_METHOD_COMP_CS,
LIBSSH2_METHOD_COMP_SC, LIB<PERSON>H2_METHOD_LANG_CS, LIB<PERSON>H2_METHOD_LANG_SC,
LIBSSH2_METHOD_SIGN_ALGO.

Returns the actual method negotiated for a particular transport parameter.
.SH RETURN VALUE
Negotiated method or NULL if the session has not yet been started.
.SH ERRORS
\fILIBSSH2_ERROR_INVAL\fP - The requested method type was invalid.

\fILIBSSH2_ERROR_METHOD_NONE\fP - no method has been set
.SH SEE ALSO
.BR libssh2_session_init_ex(3)
