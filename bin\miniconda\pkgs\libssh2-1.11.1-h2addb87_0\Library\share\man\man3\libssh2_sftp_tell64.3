.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_sftp_tell64 3 "22 Dec 2008" "libssh2 1.0" "libssh2"
.SH NAME
libssh2_sftp_tell64 - get the current read/write position indicator for a file
.SH SYNOPSIS
.nf
#include <libssh2.h>
#include <libssh2_sftp.h>

libssh2_uint64_t
libssh2_sftp_tell64(LIBSSH2_SFTP_HANDLE *handle);
.fi
.SH DESCRIPTION
\fIhandle\fP - SFTP File Handle as returned by \fBlibssh2_sftp_open_ex(3)\fP

Identify the current offset of the file handle's internal pointer.
.SH RETURN VALUE
Current offset from beginning of file in bytes.
.SH AVAILABILITY
Added in libssh2 1.0
.SH SEE ALSO
.BR libssh2_sftp_open_ex(3),
.BR libssh2_sftp_tell(3)
