.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_scp_send 3 "20 Feb 2010" "libssh2 1.2.4" "libssh2"
.SH NAME
libssh2_scp_send - convenience macro for \fIlibssh2_scp_send_ex(3)\fP calls
.SH SYNOPSIS
.nf
#include <libssh2.h>

LIBSSH2_CHANNEL *
libssh2_scp_send(LIBSSH2_SESSION *session, const char *path,
                 int mode, size_t size);
.fi
.SH DESCRIPTION
This is a macro defined in a public libssh2 header file that is using the
underlying function \fIlibssh2_scp_send_ex(3)\fP.
.SH RETURN VALUE
See \fIlibssh2_scp_send_ex(3)\fP
.SH ERRORS
See \fIlibssh2_scp_send_ex(3)\fP
.SH SEE ALSO
.BR libssh2_scp_send_ex(3)
