libssh2 1.11.1

Deprecation notices:

- Starting October 2024, the following algos go deprecated and will be
  disabled in default builds (with an option to enable them):

  - DSA: `ssh-dss` hostkeys.
      You can enable it now with `-DLIBSSH2_DSA_ENABLE`.
      Disabled by default in OpenSSH 7.0 (2015-08-11).
      Support to be removed by early 2025 from OpenSSH.
  - MD5-based MACs and hashes: `hmac-md5`, `hmac-md5-96`,
    `LIBSSH2_HOSTKEY_HASH_MD5`
      You can disable it now with `-DLIBSSH2_NO_MD5`.
      Disabled by default since OpenSSH 7.2 (2016-02-29).
  - 3DES cipher: `3des-cbc`
      You can disable it now with `-DLIBSSH2_NO_3DES`.
      Disabled by default since OpenSSH 7.4 (2016-12-19).
  - RIPEMD-160 MACs: `hmac-ripemd160`, `<EMAIL>`
      You can disable it now with `-DLIBSSH2_NO_HMAC_RIPEMD`.
      Removed in OpenSSH 7.6 (2017-10-03).
  - Blowfish cipher: `blowfish-cbc`
      You can disable it now with `-DLIBSSH2_NO_BLOWFISH`.
      Removed in OpenSSH 7.6 (2017-10-03).
  - RC4 ciphers: `arcfour`, `arcfour128`
      You can disable it now with `-DLIBSSH2_NO_RC4`.
      Removed in OpenSSH 7.6 (2017-10-03).
  - CAST cipher: `cast128-cbc`
      You can disable it now with `-DLIBSSH2_NO_CAST`.
      Removed in OpenSSH 7.6 (2017-10-03).

- Starting April 2025, above options will be deleted from the
  libssh2 codebase.

  - Default builds will also disable support for old-style, MD5-based
    encrypted private keys.
    You can disable it now with `-DLIBSSH2_NO_MD5_PEM`.

This release includes the following enhancements and bugfixes:

- autotools: fix to update `LDFLAGS` for each detected dependency (d19b6190 #1384 #1381 #1377)
- autotools: delete `--disable-tests` option, fix CI tests (e051ae34 #1271 #715 revert: 7483edfa)
- autotools: show the default for `hidden-symbols` option (a3f5594a #1269)
- autotools: enable `-Wunused-macros` with gcc (ecdf5199 #1262 #1227 #1224)
- autotools: fix dotless gcc and Apple clang version detections (89ccc83c #1232 #1187)
- autotools: show more clang/gcc version details (fb580161 #1230)
- autotools: avoid warnings in libtool stub code (96682bd5 #1227 #1224)
- autotools: sync warning enabler code with curl (5996fefe #1223)
- autotools: rename variable (ce5f208a #1222)
- autotools: picky warning options tidy-up (cdca8cff #1221)
- autotools: fix `cp` to preserve attributes and timestamp in `Makefile.am` (f64e6318)
- autotools: fix selecting WinCNG in cross-builds (and more) (00a3b88c #1187 #1186)
- autotools: use comma separator in `Requires.private` of `libssh2.pc` (7f83de14 #1124)
- autotools: remove `AB_INIT` from `configure.ac` (f4f52ccc)
- autotools: improve libz position (c89174a7 #1077 #941 #1075 #1013 regr: 4f0f4bff)
- autotools: skip tests requiring static lib if `--disable-static` (572c57c9 #1072 #663 #1056 regr: 83853f8a)
- build: stop detecting `sys/param.h` header (2677d3b0 #1418 #1415)
- build: silence warnings inside `FD_SET()`/`FD_ISSET()` macros (323a14b2 #1379)
- build: drop `-Wformat-nonliteral` warning suppressions (c452c5cc #1342)
- build: enable `-pedantic-errors` (3ec53f3e #1286)
- build: add mingw-w64 support to `LIBSSH2_PRINTF()` attribute (f8c45794 #1287)
- build: add `LIBSSH2_NO_DEPRECATED` option (b1414503 #1267 #1266 #1260 #1259)
- build: enable missing OpenSSF-recommended warnings, with fixes (afa6b865 #1257)
- build: enable more compiler warnings and fix them (7ecc309c #1224)
- build: picky warning updates (328a96b3 #1219)
- build: revert: respect autotools `DLL_EXPORT` in `libssh2.h` (481be044 #1141 #917 revert: fb1195cf)
- build: stop requiring libssl from openssl (c84745e3 #1128)
- build: tidy-up `libssh2.pc.in` variable names (5720dd9f #1125)
- build: add/fix `Requires.private` packages in `libssh2.pc` (ef538069 #1123)
- buildconf: drop (814a850c #1441 follow: fc5d7788)
- checksrc: update, check all sources, fix fallouts (1117b677 #1457)
- checksrc: sync with curl (8cd473c9 #1272)
- checksrc: fix spelling in comment (a95d401f)
- checksrc: modernise Perl file open (3d309f9b)
- checksrc: switch to dot file (d67a91aa #1052)
- ci: use Ninja with cmake (20ad047d #1458)
- ci: disable dependency tracking in autotools builds (e44f0418 #1396)
- ci: fix mbedtls runners on macOS (84411539 #1381)
- ci: enable Unity mode for most CMake builds (1bfae57b #1367 #1034)
- ci: add shellcheck job and script (d88b9bcd)
- ci: verify build and install from tarball (a86e27e8 #1362)
- ci: add reproducibility test for `maketgz` (2d765e45 #1360)
- ci: use Linux runner for BSDs, add arm64 FreeBSD 14 job (6f86b196 #1343)
- ci: do not parallelize `distcheck` job (5e65dd87 #1339)
- ci: add FreeBSD 14 job, fix issues (46333adf #1277)
- ci: add OmniOS job, fix issues (5e0ec991)
- ci: show compiler in cross/cygwin job names (c9124088)
- ci: add OpenBSD (v7.4) job + fix build error in example (0c9a8e35 #1250)
- ci: add NetBSD (v9.3) job (65c7a7a5)
- ci: update and speed up FreeBSD job (eee4e805)
- ci: use absolute path in `CMAKE_INSTALL_PREFIX` (74948816 #1247)
- ci: boost mbedTLS build speed (236e79a1 #1245)
- ci: add BoringSSL job (cmake, gcc, amd64) (c9dd3566 #1233)
- ci: fixup FreeBSD version, bump mbedTLS (fea6664e #1217)
- ci: add FreeBSD 13.2 job (a7d2a573 #1215)
- ci: mbedTLS 3.5.0 (5e190442 #1202)
- ci: update actions, use shallow clones with appveyor (d468a33f #1199)
- ci: replace `mv` + `chmod` with `install` in `Dockerfile` (5754fed6 #1175)
- ci: set file mode early in `appveyor_docker.yml` (633db55f)
- ci: add spellcheck (codespell) (a79218d3)
- ci: add MSYS builds (autotools and cmake) (d43b8d9b #1162)
- ci: add Cygwin builds (autotools and cmake) (f1e96e73 #1161)
- ci: add mingw-w64 UWP build (1215aa5f #1155 #1147)
- ci: add missing timeout to 'autotools distcheck' step (6265ffdb)
- ci: add non-static autotools i386 build, ignore GHA updates on AppVeyor (c6e137f7 #1074 #1072)
- ci: prefer `=` operator in shell snippets (e5c03043 #1073)
- ci: drop redundant/unused vars, sync var names (ab8e95bc #1059)
- ci: add i386 Linux build (with mbedTLS) (abdf40c7 #1057 #1053)
- ci/appveyor: reduce test runs (workaround for infrastructure permafails) (b5e68bdc #1461)
- ci/appveyor: increase wait for SSH server on GHA (bf3af90b)
- ci/appveyor: bump to OpenSSL 3.2.1 (53d9c1a6 #1363 #1348)
- ci/appveyor: re-enable parallel mode (e190e5b2 #1294 #884 #867)
- ci/appveyor: delete UWP job broken since Visual Studio upgrade (d0a7f1da #1275)
- ci/appveyor: YAML/PowerShell formatting, shorten variable name (06fd721f #1200)
- ci/appveyor: move to pure PowerShell (8a081fd9 #1197)
- ci/GHA: revert concurrency and improve permissions (e4c042f6)
- ci/GHA: FreeBSD 14.1, actions bump (ae04b1b9 #1424)
- ci/GHA: fix wolfSSL-from-source AES-GCM tests (1c0b07a7 #1409 #1408)
- ci/GHA: add Linux job with latest wolfSSL built from source (d4cea53f #1408 #1299 #1020)
- ci/GHA: tidy up build-from-source steps (2c633033)
- ci/GHA: show configure logs on failure and other tidy-ups (dab48398 #1403)
- ci/GHA: bump parallel jobs to nproc+1 (6f3d3bc8 #1402)
- ci/GHA: show test logs on failure (b8ffa7a5 #1401)
- ci/GHA: fix `Dockerfile` failing after Ubuntu package update (839bb84e #1400)
- ci/GHA: use ubuntu-latest with OmniOS job (50143d58)
- ci/GHA: shell syntax tidy-up (3b23e039 #1390)
- ci/GHA: bump NetBSD/OpenBSD, add NetBSD arm64 job (e980af72 #1388)
- ci/GHA: tidy up wolfSSL autotools config on macOS (5953c1f1 #1383)
- ci/GHA: shorter mbedTLS autotools workaround (736e3d7d #1382 #1381)
- ci/GHA: fix gcrypt with autotools/macOS/Homebrew/ARM64 (ae2770de #1377)
- ci/GHA: fix verbose option for autotools jobs (499b27ae #1376)
- ci/GHA: dump `config.log` on failure for macOS autotools jobs (4fa69214 #1375)
- ci/GHA: fix `autoreconf` failure on macOS/Homebrew (0b64b30b #1374)
- ci/GHA: fixup Homebrew location (for ARM runners) (6128aee0 #1373)
- ci/GHA: review/fixup auto-cancel settings (b08cfbc9 #1292)
- ci/GHA: restore curly braces in `if` (36748270 #1145)
- ci/GHA: simplify `if` strings (cab3db58 #1140)
- cmake: sync and improve Find modules, add `pkg-config` native detection (45064137 #1445 #1420)
- cmake: generate `LIBSSH2_PC_LIBS_PRIVATE` dynamically (c87f1296 #1466)
- cmake: add comment about `ibssh2.pc.in` variables (14b1b9d0)
- cmake: support absolute `CMAKE_INSTALL_INCLUDEDIR`/`CMAKE_INSTALL_LIBDIR` (d70cee36 #1465)
- cmake: rename two variables and initialize them (0fce9dcc #1464)
- cmake: prefer `find_dependency()` in `libssh2-config.cmake` (d9c2e550 #1460)
- cmake: tidy up syntax, minor improvements (9d9ee780 #1446)
- cmake: rename mbedTLS and wolfSSL Find modules (570de0f2)
- cmake: fixup version detection in mbedTLS Find module (8e3c40b2 #1444)
- cmake: mbedTLS detection tidy-ups (6d1d13c2 #1438)
- cmake: add quotes, delete ending dirseps (2bb46d44 #1437 #1166)
- cmake: sync formatting in `cmake/Find*` modules (a0310699)
- cmake: tidy up function name casing in `CopyRuntimeDependencies.cmake` (03547cb8)
- cmake: use the imported target of FindOpenSSL module (82b09f9b #1322)
- cmake: rename picky warnings script (64d6789f #1225)
- cmake: fix multiple include of libssh2 package (932d6a32 #1216)
- cmake: show crypto backend in feature summary (20387285 #1211)
- cmake: simplify showing CMake version (fc00bdd7 #1203)
- cmake: cleanup mbedTLS version detection more (4c241d5c #1196 #1192)
- cmake: delete duplicate `include()` (30eef0a6)
- cmake: improve/fix mbedTLS detection (41594675 #1192 #1191)
- cmake: tidy-up `foreach()` syntax (4a64ca14 #1180)
- cmake: verify `libssh2_VERSION` in integration tests (a20572e9)
- cmake: show cmake versions in ci (87f5769b)
- cmake: quote more strings (e9c7d3af #1173)
- cmake: add `ExternalProject` integration test (aeaefaf6 #1171)
- cmake: add integration tests (8715c3d5 #1170)
- cmake: (re-)add aliases for `add_subdirectory()` builds (4ff64ae3 #1169)
- cmake: style tidy-up (3fa5282d #1166)
- cmake: add `LIB_NAME` variable (5453fc80 #1159)
- cmake: tidy-up concatenation in `CMAKE_MODULE_PATH` (ae7d5108 #1157)
- cmake: replace `libssh2` literals with `PROJECT_NAME` variable (72fd2595 #1152)
- cmake: fix `STREQUAL` check in error branch (42d3bf13 #1151)
- cmake: cache more config values on Windows (11a03690 #1142)
- cmake: streamline invocation (f58f77b5 #1138)
- cmake: merge `set_target_properties()` calls (a9091007 #1132)
- cmake: (re-)add zlib to `Libs.private` in `libssh2.pc` (64643018 #1131)
- cmake: use `wolfssl/options.h` for detection, like autotools (c5ec6c49 #1130)
- cmake: add openssl libs to `Libs.private` in `libssh2.pc` (5cfa59d3 #1127)
- cmake: bump minimum CMake version to v3.7.0 (9cd18f45 #1126)
- cmake: CMAKE_SOURCE_DIR -> PROJECT_SOURCE_DIR (0f396aa9 #1121)
- cmake: tidy-ups (2fc36790 #1122)
- cmake: re-add `Libssh2:libssh2` for compatibility + lowercase namespace (2da13c13 #1104 #731 #1103)
- copyright: remove years from copyright headers (187d89bb #1082)
- disable DSA by default (b7ab0faa #1435 #1433)
- docs: update `INSTALL_AUTOTOOLS` (2f0efde3 #1316)
- docs: replace SHA1 with SHA256 in CMake example (766bde9f)
- example: restore `sys/time.h` for AIX (24503cb9 #1340 #1335 #1334 #1001 regr: e53aae0e)
- example: use `libssh2_socket_t` in X11 example (3f60ccb7)
- example: replace remaining libssh2_scp_recv with libssh2_scp_recv2 in output messages (8d69e63d #1258 follow: 6c84a426)
- example: fix regression in `ssh2_exec.c` (279a2e57 #1106 #861 #846 #1105 regr: b13936bd)
- example, tests: call `WSACleanup()` for each `WSAStartup()` (94b6bad3 #1283)
- example, tests: fix/silence `-Wformat-truncation=2` gcc warnings (744e059f)
- hostkey: do not advertise ssh-rsa when SHA1 is disabled (82d1b8ff #1093 #1092)
- kex: prevent possible double free of hostkey (b3465418 #1452)
- kex: always check for null pointers before calling _libssh2_bn_set_word (9f23a3bb #1423)
- kex: fix a memory leak in key exchange (19101843 #1412 #1404)
- kex: always add extension indicators to kex_algorithms (00e2a07e #1327 #1326)
- libssh2.h: add deprecated function warnings (9839ebe5 #1289 #1260)
- libssh2.h: add portable `LIBSSH2_SOCKET_CLOSE()` macro (28dbf016 #1278)
- libssh2.h: use `_WIN32` for Windows detection instead of rolling our own (631e7734 #1238)
- libssh2.pc: reference mbedcrypto pkgconfig (c149a127 #1405)
- libssh2.pc: re-add & extend support for static-only libssh2 builds (624abe27 #1119 #1114)
- libssh2.pc: don't put `@LIBS@` in pc file (1209c16d)
- mac: add empty hash functions for `mac_method_hmac_aesgcm` to not crash when e.g. setting `LIBSSH2_METHOD_CRYPT_CS` (b2738391 #1321)
- mac: handle low-level errors (f64885b6 #1297)
- Makefile.mk: delete Windows-focused raw GNU Make build (43485579 #1204)
- maketgz: reproducible tarballs/zip, display tarball hashes (d52fe1b4 #1357 #1359)
- maketgz: `set -eu`, reproducibility, improve zip, add CI test (cba7f975 #1353)
- man: improve `libssh2_userauth_publickey_from*` manpages (581b72aa #1347 #1308 #652)
- man: fix double spaces and dash escaping (a3ffc422 #1210)
- man: add description to `libssh2_session_get_blocking.3` (67e39091 #1185)
- mbedtls: always init ECDSA mbedtls_pk_context (a50d7deb #1430)
- mbedtls: correctly initialize values (ECDSA) (1701d5c0 #1428 #1421)
- mbedtls: expose `mbedtls_pk_load_file()` for our use (1628f6ca #1421 #1393 #1349 follow: e973493f)
- mbedtls: add workaround + FIXME to build with 3.6.0 (2e4c5ec4 #1349)
- mbedtls: improve disabling `-Wredundant-decls` (ecec68a2 #1226 #1224)
- mbedtls: include `version.h` for `MBEDTLS_VERSION_NUMBER` (9d7bc253 #1095 #1094)
- mbedtls: use more `size_t` to sync up with `crypto.h` (1153ebde #1054 #879 #846 #1053)
- md5: allow disabling old-style encrypted private keys at build-time (eb9f9de2 #1181)
- mingw: fix printf mask for 64-bit integers (36c1e1d1 #1091 #876 #846 #1090)
- misc: flatten `_libssh2_explicit_zero` if tree (74e74288 #1149)
- NMakefile: delete (c515eed3 #1134 #1129)
- openssl: free allocated resources when using openssl3 (b942bad1 #1459)
- openssl: fix memory leaks in `_libssh2_ecdsa_curve_name_with_octal_new` and `_libssh2_ecdsa_verify` (8d3bc19b #1449)
- openssl: fix calculating DSA public key with OpenSSL 3 (8b3c6e9d #1380)
- openssl: initialize BIGNUMs to NULL in `gen_publickey_from_dsa` for OpenSSL 3 (f1133c75 #1320)
- openssl: fix cppcheck found NULL dereferences (f2945905 #1304)
- openssl: delete internal `read_openssh_private_key_from_memory()` (34aff5ff #1306)
- openssl: use OpenSSL 3 HMAC API, add `no-deprecated` CI job (363dcbf4 #1243 #1235 #1207)
- openssl: make a function static, add `#ifdef` comments (efee9133 #1246 #248 follow: 03092292)
- openssl: fix DSA code to use OpenSSL 3 API (82581941 #1244 #1207)
- openssl: fix `EC_KEY` reference with OpenSSL 3 `no-deprecated` build (487152f4 #1236 #1235 #1207)
- openssl: use non-deprecated APIs with OpenSSL 3.x (b0ab005f #1207)
- openssl: silence `-Wunused-value` warnings (bf285500 #1205)
- openssl: use automatic initialization with LibreSSL 2.7.0+ (d79047c9 #1146 #302)
- openssl: add missing check for `LIBRESSL_VERSION_NUMBER` before use (4a42f42e #1117 #1115)
- os400: drop vsprintf() use (40e817ff #1462 #1457)
- os400: Add two recent files to the distribution (e4c65e5b #1364)
- os400: fix shellcheck warnings in scripts (fixups) (81341e1e #1366 #1364 #1358)
- os400: fix shellcheck warnings in scripts (c6625707 #1358)
- os400: maintain up to date (8457c37a #1309)
- packet: properly bounds check packet_authagent_open() (88a960a8 #1179)
- pem: fix private keys encrypted with AES-GCM methods (e87bdefa #1133)
- reuse: upgrade to `REUSE.toml` (70b8bf31 #1419)
- reuse: fix duplicate copyright warning (b9a4ed83)
- reuse: comply with 3.1 spec and 2.0.0 checker (fe6239a1 #1102 #1101 #1098)
- reuse: provide SPDX identifiers (f6aa31f4 #1084)
- scp: fix missing cast for targets without large file support (c317e06f #1060 #1057 #1002 regr: 5db836b2)
- session: support server banners up to 8192 bytes (was: 256) (1a9e8811 #1443 #1442)
- session: add `libssh2_session_callback_set2()` (c0f69548 #1285)
- session: handle EINTR from send/recv/poll/select to try again as the error is not fatal (798ed4a7 #1058 #955)
- sftp: increase SFTP_HANDLE_MAXLEN back to 4092 (75de6a37 #1422)
- sftp: implement <EMAIL> (fb652746 #1386)
- src: implement <EMAIL> (492bc543 #1426 #584)
- src: use `UINT32_MAX` (dc206408 #1413)
- src: fix type warning in `libssh2_sftp_unlink` macro (ac2e8c73 #1406)
- src: check the return value from `_libssh2_bn_*()` functions (95c824d5 #1354)
- src: support RSA-SHA2 cert-based authentication (rsa-sha2-512_cert and rsa-sha2-256_cert) (3a6ab70d #1314)
- src: check hash update/final success (4718ede4 #1303 #1301)
- src: check hash init success (2ed9eb92 #1301)
- src: add 'strict KEX' to fix CVE-2023-48795 "Terrapin Attack" (d34d9258 #1291 #1290)
- src: disable `-Wsign-conversion` warnings, add option to re-enable (6e451669 #1284 #1257)
- src: fix gcc 13 `-Wconversion` warning on Darwin (8cca7b77 #1209 follow: 08354e0a)
- src: drop a redundant `#include` (1f0174d0 #1153)
- src: improve MSVC C4701 warning fix (8b924999 #1086 #876 #1083)
- src: bump `hash_len` to `size_t` in `LIBSSH2_HOSTKEY_METHOD` (8b917d76 #1076)
- src: bump DSA and ECDSA sign `hash_len` to `size_t` (7b8e0225 #1055)
- tests: avoid using `MAXPATHLEN`, for portability (12427f4f #1415 #198 #1414)
- tests: fix excluding AES-GCM tests (fbd9d192 #1410)
- tests: drop default cygpath option `-u` (38e50aa0)
- tests: fix shellcheck issues in `test_sshd.test` (a2ac8c55)
- tests: sync port number type with the rest of codebase (eb996af8)
- tests: fall back to `$LOGNAME` for username (5326a5ce #1241 #1240)
- tests: show cmake version used in integration tests (2cd2f40e #1201)
- tests: formatting and tidy-ups (e61987a3)
- tests: replace FIXME with comments (1a99a86a)
- tests: add aes256-gcm encrypted key test (802336cf #1135 #1133)
- tests: trap signals in scripts (b2916b28 #1098)
- tests: cast to avoid `-Wchar-subscripts` with Cygwin (43df6a46 #1081 #1080)
- test_read: make it run without Docker (57e9d18e #1139)
- test_sshd.test: show sshd and test connect logs on harness failure (299c2040 #1097)
- test_sshd.test: set a safe PID directory (e8cabdcf #1089)
- test_sshd.test: minor cleanups (d29eea1d)
- tidy-up: link updates (c905bfd2 #1434)
- tidy-up: typo in comment (792e1b6f)
- tidy-up: fix typo found by codespell (706ec36d)
- tidy-up: bump casts from int to long for large C99 types in printfs (2e5a8719 #1264 #1257)
- tidy-up: `unsigned` -> `unsigned int` (b136c379)
- tidy-up: stop using leading underscores in macro names (c6589b88 #1248)
- tidy-up: around `stdint.h` (bfa00f1b #1212)
- tidy-up: fix typo in `readme.vms` (a9a79e7a)
- tidy-up: use built-in `_WIN32` macro to detect Windows (6fbc9505 #1195)
- tidy-up: drop `www.` from `www.libssh2.org` (6e3e8839 #1172)
- tidy-up: delete duplicate word from comment (76307435)
- tidy-up: avoid exclamations, prefer single quotes, in outputs (003fb454 #1079)
- TODO: disable or drop weak algos (0b4bdc85 #1261)
- transport: fix unstable connections over non-blocking sockets (de004875 #1454 #720 #1431 #1397)
- transport: check ETM on remote end when receiving (bde10825 #1332 #1331)
- transport: fix incorrect byte offset in debug message (2388a3aa #1096)
- userauth: avoid oob with huge interactive kbd response (f3a85cad #1337)
- userauth: add a new structure to separate memory read and file read (63b4c20e #773)
- userauth: check whether `*key_method` is a NULL pointer instead of `key_method` (bec57c40)
- wincng: fix `DH_GEX_MAXGROUP` set higher than supported (48584671 #1372 #493)
- wincng: add to ci/GHA, add `./configure` option `--enable-ecdsa-wincng` (3f98bfb0 #1368 #1315)
- wincng: add ECDSA support for host and user authentication (3e723437 #1315)
- wincng: prefer `ULONG`/`DWORD` over `unsigned long` (186c1d63 #1165)
- wincng: tidy-ups (7bb669b5 #1164)
- wolfssl: drop header path hack (8ae1b2d7 #1439)
- wolfssl: fix `EVP_Cipher()` use with v5.6.0 and older (a5b0fac2 #1407 #1394 #797 #1299 #1020)
- wolfssl: bump version in upstream issue comment (5cab802c)
- wolfssl: require v5.4.0 for AES-GCM (260a721c #1411 #1299 #1020)
- wolfssl: enable debug logging in wolfSSL when compiled in (76e7a68a #1310)

This release would not have looked like this without help, code, reports and
advice from friends like these:

  Viktor Szakats, Michael Buckley, Patrick Monnerat, Ren Mingshuai,
  Will Cosgrove, Daniel Stenberg, Josef Cejka, Nicolas Mora, Ryan Kelley,
  Aaron Stone, Adam, Anders Borum, András Fekete, Andrei Augustin, binary1248,
  Brian Inglis, brucsc on GitHub, concussious on github, Dan Fandrich,
  dksslq on github, Haowei Hsu, Harmen Stoppels, Harry Mallon, Jack L,
  Jakob Egger, Jiwoo Park, João M. S. Silva, Joel Depooter, Johannes Passing,
  Jose Quaresma, Juliusz Sosinowicz, Kai Pastor, Kenneth Davidson,
  klux21 on github, Lyndon Brown, Marc Hoersken, mike-jumper, naddy,
  Nursan Valeyev, Paul Howarth, PewPewPew, Radek Brich, rahmanih on github,
  rolag on github, Seo Suchan, shubhamhii on github, Steve McIntyre,
  Tejaswi Kandula, Tobias Stoeckmann, Trzik, Xi Ruoyao
