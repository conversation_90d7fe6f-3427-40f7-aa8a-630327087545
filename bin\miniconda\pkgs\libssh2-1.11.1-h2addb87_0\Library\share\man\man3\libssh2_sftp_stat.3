.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_sftp_stat 3 "20 Feb 2010" "libssh2 1.2.4" "libssh2"
.SH NAME
libssh2_sftp_stat - convenience macro for \fIlibssh2_sftp_fstat_ex(3)\fP calls
.SH SYNOPSIS
.nf
#include <libssh2.h>
#include <libssh2_sftp.h>

int
libssh2_sftp_stat(LIBSSH2_SFTP *sftp, const char *path,
                  LIBSSH2_SFTP_ATTRIBUTES *attrs);
.fi
.SH DESCRIPTION
This is a macro defined in a public libssh2 header file that is using the
underlying function \fIlibssh2_sftp_fstat_ex(3)\fP.
.SH RETURN VALUE
See \fIlibssh2_sftp_fstat_ex(3)\fP
.SH ERRORS
See \fIlibssh2_sftp_fstat_ex(3)\fP
.SH SEE ALSO
.BR libssh2_sftp_fstat_ex(3)
