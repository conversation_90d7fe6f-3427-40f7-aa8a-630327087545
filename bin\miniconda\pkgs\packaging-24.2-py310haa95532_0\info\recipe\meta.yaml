# This file created by conda-build 24.11.2
# meta.yaml template originally from:
# C:\b\abs_3by6s2fa66\clone\recipe, last modified Tue Dec 17 21:48:44 2024
# ------------------------------------------------

package:
  name: packaging
  version: '24.2'
source:
  sha256: c228a6dc5e932d346bc5739379109d49e8853dd8223571c7c5b55260edc0b97f
  url: https://pypi.io/packages/source/p/packaging/packaging-24.2.tar.gz
build:
  number: '0'
  script: C:\\b\\abs_3by6s2fa66\\croot\\packaging_1734472138782\\_h_env\\python.exe
    -m pip install . --no-deps --no-build-isolation -vv
  string: py310haa95532_0
requirements:
  host:
    - bzip2 1.0.8 h2bbff1b_6
    - ca-certificates 2024.11.26 haa95532_0
    - flit-core 3.9.0 py310he100be2_1
    - libffi 3.4.4 hd77b12b_1
    - openssl 3.0.15 h827c3e9_0
    - pip 24.2 py310haa95532_0
    - python 3.10.16 h4607a30_1
    - setuptools 75.1.0 py310haa95532_0
    - sqlite 3.45.3 h2bbff1b_0
    - tk 8.6.14 h0416ee5_0
    - tzdata 2024b h04d1e81_0
    - vc 14.40 haa95532_2
    - vs2015_runtime 14.42.34433 h9531ae6_2
    - wheel 0.44.0 py310haa95532_0
    - xz 5.4.6 h8cc25b3_1
    - zlib 1.2.13 h8cc25b3_1
  run:
    - python >=3.10,<3.11.0a0
test:
  commands:
    - pip check
    - pytest -v tests
  imports:
    - packaging
  requires:
    - pip
    - pretend
    - pytest >=6.2.0
  source_files:
    - tests
about:
  description: 'Reusable core utilities for various Python Packaging interoperability
    specifications.

    This library provides utilities that implement the interoperability specifications

    which have clearly one correct behaviour (eg: PEP 440) or benefit greatly from
    having

    a single shared implementation (eg: PEP 425).

    '
  dev_url: https://github.com/pypa/packaging
  doc_url: https://packaging.pypa.io
  home: https://github.com/pypa/packaging
  license: Apache-2.0 or BSD-2-Clause
  license_family: Apache
  license_file: LICENSE
  summary: Core utilities for Python packages
extra:
  copy_test_source_files: true
  final: true
  flow_run_id: 3b4bc171-6f20-41ab-b8d5-43c5c93279a8
  recipe-maintainers:
    - jakirkham
    - mingwandroid
    - nicoddemus
  remote_url: **************:AnacondaRecipes/packaging-feedstock.git
  sha: f16c556e1fa973741f31bf4c92e0e44e66f6ff9e
  skip-lints:
    - missing_wheel
