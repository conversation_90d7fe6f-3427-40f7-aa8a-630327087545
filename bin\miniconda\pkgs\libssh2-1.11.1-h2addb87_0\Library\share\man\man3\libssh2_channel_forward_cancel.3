.\" Copyright (C) The libssh2 project and its contributors.
.\" SPDX-License-Identifier: BSD-3-Clause
.TH libssh2_channel_forward_cancel 3 "1 Jun 2007" "libssh2 0.15" "libssh2"
.SH NAME
libssh2_channel_forward_cancel - cancel a forwarded TCP port
.SH SYNOPSIS
.nf
#include <libssh2.h>

int
libssh2_channel_forward_cancel(LIBSSH2_LISTENER *listener);
.fi
.SH DESCRIPTION
\fIlistener\fP - Forwarding listener instance as returned by
.BR libssh2_channel_forward_listen_ex(3)

Instruct the remote host to stop listening for new connections on a previously
requested host/port.
.SH RETURN VALUE
Return 0 on success or negative on failure. It returns
LIBSSH2_ERROR_EAGAIN when it would otherwise block. While
LIBSSH2_ERROR_EAGAIN is a negative number, it is not really a failure per se.
.SH ERRORS
\fILIBSSH2_ERROR_ALLOC\fP - An internal memory allocation call failed.

\fILIBSSH2_ERROR_SOCKET_SEND\fP - Unable to send data on socket.
.SH SEE ALSO
.BR libssh2_channel_forward_listen_ex(3)
